'use client';

import {
  AlertTriangle,
  RefreshCw,
  CreditCard,
  Home,
  Phone,
} from 'lucide-react';
import React from 'react';

import { Button } from '@/components/ui/button';

interface PaymentErrorProps {
  error: Error & { digest?: string };
  reset: () => void;
}

/**
 * 支付页面错误边界组件
 * 处理支付相关页面中的错误
 */
export default function PaymentError({ error, reset }: PaymentErrorProps) {
  React.useEffect(() => {
    // 记录支付相关错误（这类错误需要特别关注）
    console.error('Payment error:', error);

    // 可以集成支付监控服务
    // PaymentMonitoring.captureError(error);
  }, [error]);

  return (
    <div className='flex min-h-screen items-center justify-center bg-gray-50 p-4 dark:bg-gray-900'>
      <div className='w-full max-w-md text-center'>
        <div className='mb-6'>
          <div className='mx-auto flex h-20 w-20 items-center justify-center rounded-full bg-red-100 dark:bg-red-900/20'>
            <AlertTriangle className='h-10 w-10 text-red-600' />
          </div>
        </div>

        <h1 className='mb-4 text-2xl font-bold text-gray-900 dark:text-gray-100'>
          支付处理错误
        </h1>

        <p className='mb-6 text-gray-600 dark:text-gray-400'>
          支付过程中遇到了问题。请不要担心，您的资金是安全的。请尝试重新处理或联系客服。
        </p>

        {/* 开发环境下显示错误详情 */}
        {process.env.NODE_ENV === 'development' && (
          <div className='mb-6 rounded-lg bg-red-50 p-4 text-left dark:bg-red-900/20'>
            <h3 className='mb-2 font-semibold text-red-800 dark:text-red-200'>
              支付错误详情:
            </h3>
            <pre className='text-xs text-red-700 dark:text-red-300 overflow-auto'>
              {error.message}
            </pre>
            {error.digest && (
              <p className='mt-2 text-xs text-red-600 dark:text-red-400'>
                错误ID: {error.digest}
              </p>
            )}
          </div>
        )}

        <div className='flex flex-col gap-3'>
          <Button
            onClick={reset}
            variant='default'
            className='flex items-center gap-2'
          >
            <RefreshCw className='h-4 w-4' />
            重试支付
          </Button>

          <Button
            onClick={() => (window.location.href = '/wallet')}
            variant='outline'
            className='flex items-center gap-2'
          >
            <CreditCard className='h-4 w-4' />
            返回钱包
          </Button>

          <Button
            onClick={() => (window.location.href = '/')}
            variant='ghost'
            className='flex items-center gap-2'
          >
            <Home className='h-4 w-4' />
            返回首页
          </Button>
        </div>

        {/* 安全保障提示 */}
        <div className='mt-8 rounded-lg bg-blue-50 p-4 dark:bg-blue-900/20'>
          <h3 className='mb-2 font-semibold text-blue-900 dark:text-blue-100'>
            资金安全保障
          </h3>
          <ul className='text-sm text-blue-800 dark:text-blue-200 space-y-1'>
            <li>• 您的支付信息受到银行级加密保护</li>
            <li>• 未完成的交易不会产生费用</li>
            <li>• 如有疑问，请及时联系客服</li>
          </ul>
        </div>

        {/* 客服联系方式 */}
        <div className='mt-4 rounded-lg bg-gray-100 p-4 dark:bg-gray-800'>
          <div className='flex items-center justify-center space-x-2 text-sm text-gray-600 dark:text-gray-400'>
            <Phone className='h-4 w-4' />
            <span>遇到问题？联系客服获得帮助</span>
          </div>
        </div>
      </div>
    </div>
  );
}
