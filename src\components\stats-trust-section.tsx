"use client";

import { motion } from "framer-motion";
import { 
  Users, 
  TrendingUp, 
  Shield, 
  Clock, 
  Award, 
  Globe, 
  CheckCircle, 
  Star,
  Zap,
  Heart
} from "lucide-react";
import { useTranslations } from "next-intl";
import { useEffect, useState } from "react";

import { Badge } from "@/components/ui/badge";
import { Card, CardContent } from "@/components/ui/card";

export function StatsTrustSection() {
  const t = useTranslations("HomePage");
  const [animatedStats, setAnimatedStats] = useState({
    users: 0,
    successRate: 0,
    volume: 0,
    support: 24
  });

  // 数字动画效果
  useEffect(() => {
    const animateNumbers = () => {
      const duration = 2000;
      const steps = 60;
      const interval = duration / steps;

      let step = 0;
      const timer = setInterval(() => {
        step++;
        const progress = step / steps;
        const easeOut = 1 - Math.pow(1 - progress, 3);

        setAnimatedStats({
          users: Math.floor(20000 * easeOut),
          successRate: Math.floor(98.5 * easeOut * 10) / 10,
          volume: Math.floor(13 * easeOut),
          support: 24
        });

        if (step >= steps) {
          clearInterval(timer);
        }
      }, interval);
    };

    const observer = new IntersectionObserver(
      (entries) => {
        if (entries[0].isIntersecting) {
          animateNumbers();
          observer.disconnect();
        }
      },
      { threshold: 0.3 }
    );

    const element = document.getElementById('stats-section');
    if (element) {
      observer.observe(element);
    }

    return () => observer.disconnect();
  }, []);

  const stats = [
    {
      icon: Users,
      value: `${(animatedStats.users / 1000).toFixed(0)}K+`,
      label: t("brand.stats.users"),
      color: "from-blue-500 to-blue-600",
      bgColor: "from-blue-50 to-blue-100",
      description: t("brand.stats.descriptions.users")
    },
    {
      icon: TrendingUp,
      value: `${animatedStats.successRate}%`,
      label: t("brand.stats.successRate"),
      color: "from-green-500 to-green-600",
      bgColor: "from-green-50 to-green-100",
      description: t("brand.stats.descriptions.successRate")
    },
    {
      icon: Shield,
      value: `$${animatedStats.volume}M+`,
      label: t("brand.stats.volume"),
      color: "from-purple-500 to-purple-600",
      bgColor: "from-purple-50 to-purple-100",
      description: t("brand.stats.descriptions.volume")
    },
    {
      icon: Clock,
      value: `${animatedStats.support}/7`,
      label: t("brand.stats.support"),
      color: "from-orange-500 to-orange-600",
      bgColor: "from-orange-50 to-orange-100",
      description: t("brand.stats.descriptions.support")
    }
  ];

  const trustIndicators = [
    {
      icon: Shield,
      title: t("brand.trustIndicators.security.title"),
      description: t("brand.trustIndicators.security.description"),
      color: "text-blue-600"
    },
    {
      icon: Award,
      title: t("brand.trustIndicators.certification.title"),
      description: t("brand.trustIndicators.certification.description"),
      color: "text-green-600"
    },
    {
      icon: Globe,
      title: t("brand.trustIndicators.globalService.title"),
      description: t("brand.trustIndicators.globalService.description"),
      color: "text-purple-600"
    },
    {
      icon: Zap,
      title: t("brand.trustIndicators.response.title"),
      description: t("brand.trustIndicators.response.description"),
      color: "text-orange-600"
    }
  ];

  return (
    <section id="stats-section" className="py-16 md:py-24 bg-gradient-to-b from-gray-50 to-white dark:from-gray-900 dark:to-gray-800">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* 标题区域 */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.8 }}
          className="text-center mb-16"
        >
          <Badge className="bg-gradient-to-r from-gradient-primary-from to-gradient-primary-to text-white px-6 py-2 text-sm font-medium mb-6">
            <Star className="h-4 w-4 mr-2" />
            {t("brand.stats.title")}
          </Badge>
          <h2 className="text-3xl md:text-4xl lg:text-5xl font-bold text-text-primary dark:text-text-primary mb-6">
            {t("brand.stats.title")}
          </h2>
          <p className="text-xl text-text-secondary dark:text-text-secondary max-w-3xl mx-auto">
            {t("brand.stats.subtitle")}
          </p>
        </motion.div>

        {/* 统计数据网格 */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8 mb-20">
          {stats.map((stat, index) => (
            <motion.div
              key={index}
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.6, delay: index * 0.1 }}
              whileHover={{ scale: 1.05, y: -5 }}
              className="group"
            >
              <Card className="h-full bg-card/80 dark:bg-card/80 backdrop-blur-sm border-0 shadow-lg hover:shadow-2xl transition-all duration-300">
                <CardContent className="p-8 text-center">
                  <div className={`inline-flex p-4 rounded-2xl bg-gradient-to-r ${stat.bgColor} dark:from-gradient-primary-from/20 dark:to-gradient-primary-to/20 mb-6 group-hover:scale-110 transition-transform duration-300`}>
                    <stat.icon className={`h-8 w-8 bg-gradient-to-r ${stat.color} bg-clip-text text-transparent`} />
                  </div>
                  <div className="text-4xl md:text-5xl font-bold text-text-primary dark:text-text-primary mb-3">
                    {stat.value}
                  </div>
                  <div className="text-lg font-semibold text-text-secondary dark:text-text-secondary mb-2">
                    {stat.label}
                  </div>
                  <div className="text-sm text-text-muted dark:text-text-muted">
                    {stat.description}
                  </div>
                </CardContent>
              </Card>
            </motion.div>
          ))}
        </div>

        {/* 信任指标 */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.8, delay: 0.2 }}
          className="bg-card dark:bg-card rounded-3xl shadow-xl p-8 md:p-12"
        >
          <div className="text-center mb-12">
            <h3 className="text-2xl md:text-3xl font-bold text-text-primary dark:text-text-primary mb-4">
              {t("brand.whyChooseUs.title")}
            </h3>
            <p className="text-lg text-text-secondary dark:text-text-secondary">
              {t("brand.whyChooseUs.description")}
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            {trustIndicators.map((indicator, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                viewport={{ once: true }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
                className="text-center group"
              >
                <div className="inline-flex p-4 rounded-2xl bg-muted dark:bg-muted group-hover:bg-muted/80 dark:group-hover:bg-muted/80 transition-colors duration-300 mb-4">
                  <indicator.icon className={`h-8 w-8 ${indicator.color} group-hover:scale-110 transition-transform duration-300`} />
                </div>
                <h4 className="text-lg font-semibold text-text-primary dark:text-text-primary mb-2">
                  {indicator.title}
                </h4>
                <p className="text-text-secondary dark:text-text-secondary">
                  {indicator.description}
                </p>
              </motion.div>
            ))}
          </div>

          {/* 底部认证标识 */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.8, delay: 0.4 }}
            className="flex flex-wrap justify-center items-center gap-6 mt-12 pt-8 border-t border-border dark:border-border"
          >
            <Badge variant="secondary" className="bg-gradient-primary-from/10 dark:bg-gradient-primary-from/20 text-gradient-primary-from dark:text-gradient-primary-from border-gradient-primary-from/20 dark:border-gradient-primary-from/30">
              <Shield className="h-4 w-4 mr-2" />
              {t("brand.stats.certified")}
            </Badge>
            <Badge variant="secondary" className="bg-success/10 dark:bg-success/20 text-success dark:text-success border-success/20 dark:border-success/30">
              <Globe className="h-4 w-4 mr-2" />
              {t("brand.stats.global")}
            </Badge>
            <Badge variant="secondary" className="bg-gradient-primary-to/10 dark:bg-gradient-primary-to/20 text-gradient-primary-to dark:text-gradient-primary-to border-gradient-primary-to/20 dark:border-gradient-primary-to/30">
              <Heart className="h-4 w-4 mr-2" />
              {t("brand.userTrust")}
            </Badge>
          </motion.div>
        </motion.div>
      </div>
    </section>
  );
}
