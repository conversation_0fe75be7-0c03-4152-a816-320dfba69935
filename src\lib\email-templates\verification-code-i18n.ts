import { emailStyles } from '@/hooks/useEmailTranslation';
import { getEmailTranslations, type SupportedLanguage } from '@/lib/email-translations';

export interface VerificationCodeData {
  userName: string;
  userEmail: string;
  verificationCode: string;
  expiresIn?: number; // 过期时间（分钟）
  language?: SupportedLanguage; // 语言设置
  action:
    | 'login'
    | 'register'
    | 'reset-password'
    | 'verify-current-email'
    | 'change-email';
}

// 获取操作类型描述 - 使用集中化翻译系统
function getActionText(
  action: string,
  language: SupportedLanguage = 'zh'
): { title: string; description: string } {
  const translations = getEmailTranslations(language);

  switch (action) {
    case 'login':
      return {
        title: translations.notifications.verification.login.title,
        description: translations.notifications.verification.login.description,
      };
    case 'register':
      return {
        title: translations.notifications.verification.register.title,
        description: translations.notifications.verification.register.description,
      };
    case 'reset-password':
      return {
        title: translations.notifications.verification.resetPassword.title,
        description: translations.notifications.verification.resetPassword.description,
      };
    case 'verify-current-email':
      return {
        title: translations.notifications.verification.verifyEmail.title,
        description: translations.notifications.verification.verifyEmail.description,
      };
    case 'change-email':
      return {
        title: translations.notifications.verification.changeEmail.title,
        description: translations.notifications.verification.changeEmail.description,
      };
    default:
      return {
        title: translations.notifications.verification.verifyEmail.title,
        description: translations.notifications.verification.verifyEmail.description,
      };
  }
}

export const verificationCodeTemplateI18n = (
  data: VerificationCodeData
): string => {
  const language = data.language || 'zh';
  const actionText = getActionText(data.action, language);
  const expiresIn = data.expiresIn || 10; // 默认10分钟过期
  const langAttr = language === 'en' ? 'en' : 'zh-CN';

  // 使用集中化翻译系统
  const t = getEmailTranslations(language);

  return `
<!DOCTYPE html>
<html lang="${langAttr}">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>${actionText.title} - ${t.common.brandName}</title>
  <style>
    ${emailStyles}
  </style>
</head>
<body>
  <div class="email-container">
    <!-- Header -->
    <div class="email-header">
      <h1>${t.common.brandName}</h1>
    </div>

    <!-- Content -->
    <div class="email-content">
      <h2 style="color: #1e293b; margin-top: 0; text-align: center;">${actionText.title}</h2>

      <p style="margin-bottom: 20px; text-align: center;">
        ${t.common.greeting}, ${data.userName}！
      </p>

      <p style="margin-bottom: 30px; text-align: center; color: #64748b;">
        ${actionText.description}
      </p>

      <!-- Verification Code Display -->
      <p style="text-align: center; margin-bottom: 8px; font-weight: 500; color: #1e293b;">
        ${t.notifications.verification.codeLabel}
      </p>
      <div class="verification-code">
        ${data.verificationCode}
      </div>

      <!-- Expiry Notice -->
      <div class="warning-box">
        <p style="margin: 0; font-weight: 500;">
          ⏰ ${t.notifications.verification.expiresLabel}: ${expiresIn} ${t.ui.labels.minutes}
        </p>
      </div>

      <!-- Security Notice -->
      <div class="info-box">
        <p style="margin: 0; font-weight: 500;">
          🔒 ${t.notifications.verification.securityWarning}
        </p>
        <ul style="margin: 8px 0; padding-left: 20px; color: #64748b;">
          ${t.notifications.verification.securityTips.map(tip =>
            `<li>${tip.replace('{minutes}', expiresIn.toString())}</li>`
          ).join('')}
        </ul>
      </div>

      <p style="margin-top: 30px; color: #64748b; text-align: center; font-size: 14px;">
        ${t.common.autoMessage}
      </p>
    </div>

    <!-- Footer -->
    <div class="email-footer">
      <p>${t.common.regards},<br>${t.common.team}</p>
      <div class="email-footer-links">
        <p style="margin: 8px 0; color: #64748b; font-size: 12px;">
          ${t.common.autoMessage}
        </p>
        <p style="margin: 8px 0; color: #64748b; font-size: 12px;">
          ${t.common.copyright}
        </p>
      </div>
    </div>
  </div>
</body>
</html>
  `.trim();
};

// 向后兼容的导出
export const verificationCodeTemplate = verificationCodeTemplateI18n;
export const verificationCodeI18nTemplate = verificationCodeTemplateI18n;
