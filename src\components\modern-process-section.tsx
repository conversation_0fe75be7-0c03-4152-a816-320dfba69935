"use client";

import { motion } from "framer-motion";
import {
  Upload,
  Search,
  CheckCircle,
  DollarSign,
  ArrowRight,
  Play,
  Target,
  Users,
  Sparkles,
  Eye,
  FileText,
  HandHeart,
  Zap,
  Settings,
  CheckCircle2,
  TrendingUp,
  Coins,
  Star,
  // --- New Icons for Redesign ---
  // For Publishing Flow
  Lightbulb,    // 定义与发布 (灵感)
  BrainCircuit, // 智能匹配 (AI)
  Link,         // 智能匹配 (链接)
  MessageSquare,// 协作执行 (沟通)
  Clock,        // 协作执行 (时间)
  ShieldCheck,  // 验收付款 (安全)

  // For Completing Flow
  Compass,      // 发现机遇 (探索)
  Briefcase,    // 发现机遇 (工作)
  Rocket,       // 申请竞标 & 高效执行 (高效)
  Award,        // 申请竞标 (技能)
  Flame,        // 申请竞标 (热情)
  Shield,       // 收获成长 (信誉)
  BarChart      // 收获成长 (增长)
} from "lucide-react";
import { useTranslations } from "next-intl";
import { useState } from "react";

import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";

export function ModernProcessSection() {
  const t = useTranslations("HomePage");
  const [activeStep, setActiveStep] = useState(0);
  const [activeTab, setActiveTab] = useState("post");

  const postTaskSteps = [
    {
      icon: FileText,
      title: `1. ${t("process.publish.steps.post.title")}`,
      description: t("process.publish.steps.post.description"),
      details: t("process.stepDetails.postTask.step1"),
      color: "from-blue-500 to-purple-600",
      bgColor: "from-blue-50 to-purple-100"
    },
    {
      icon: BrainCircuit,
      title: `2. ${t("process.publish.steps.review.title")}`,
      description: t("process.publish.steps.review.description"),
      details: t("process.stepDetails.postTask.step2"),
      color: "from-green-500 to-teal-500",
      bgColor: "from-green-50 to-teal-100"
    },
    {
      icon: MessageSquare,
      title: `3. ${t("process.publish.steps.accept.title")}`,
      description: t("process.publish.steps.accept.description"),
      details: t("process.stepDetails.postTask.step3"),
      color: "from-yellow-500 to-orange-500",
      bgColor: "from-yellow-50 to-orange-100"
    },
    {
      icon: ShieldCheck,
      title: `4. ${t("process.publish.steps.settle.title")}`,
      description: t("process.publish.steps.settle.description"),
      details: t("process.stepDetails.postTask.step4"),
      color: "from-red-500 to-pink-500",
      bgColor: "from-red-50 to-pink-100"
    }
  ];

  const completeTaskSteps = [
    {
      icon: Compass,
      title: `1. ${t("process.complete.steps.browse.title")}`,
      description: t("process.complete.steps.browse.description"),
      details: "我们的智能推荐系统会为您筛选最合适的项目，让您专注于自己擅长的领域。",
      color: "from-blue-500 to-sky-500",
      bgColor: "from-blue-50 to-sky-100"
    },
    {
      icon: Rocket,
      title: `2. ${t("process.complete.steps.accept.title")}`,
      description: t("process.complete.steps.accept.description"),
      details: "用您的才华和热情打动发布者，赢得任务执行权，开启您的项目之旅。",
      color: "from-green-500 to-lime-500",
      bgColor: "from-green-50 to-lime-100"
    },
    {
      icon: CheckCircle2,
      title: `3. ${t("process.complete.steps.execute.title")}`,
      description: t("process.complete.steps.execute.description"),
      details: "在规定时间内交付卓越的工作成果，每一次成功交付都将提升您的专业信誉。",
      color: "from-purple-500 to-violet-500",
      bgColor: "from-purple-50 to-violet-100"
    },
    {
      icon: Coins,
      title: `4. ${t("process.complete.steps.reward.title")}`,
      description: t("process.complete.steps.reward.description"),
      details: "不仅获得经济回报，还能积累宝贵经验和信誉，为未来的发展奠定坚实基础。",
      color: "from-orange-500 to-amber-500",
      bgColor: "from-orange-50 to-amber-100"
    }
  ];

  return (
    <section id="process" className="py-16 md:py-24 bg-gradient-to-b from-gray-50 to-white dark:from-gray-900 dark:to-gray-800">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* 标题区域 */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.8 }}
          className="text-center mb-16"
        >
          <Badge className="bg-gradient-to-r from-gradient-primary-from to-gradient-primary-to text-white px-6 py-2 text-sm font-medium mb-6">
            <Target className="h-4 w-4 mr-2" />
            {t("process.title")}
          </Badge>
          <h2 className="text-3xl md:text-4xl lg:text-5xl font-bold text-text-primary dark:text-text-primary mb-6">
            {t("process.title")}
          </h2>
          <p className="text-xl text-text-secondary dark:text-text-secondary max-w-3xl mx-auto">
            {t("process.subtitle")}
          </p>
        </motion.div>

        {/* 流程选项卡 - 修复高度和重叠问题 */}
        <Tabs defaultValue="post" className="w-full" onValueChange={setActiveTab}>
          <div className="mb-8 md:mb-12">
            <TabsList className="grid w-full grid-cols-2 bg-card dark:bg-card shadow-xl rounded-2xl p-2 max-w-2xl mx-auto border border-border dark:border-border h-auto min-h-[64px]">
              <TabsTrigger
                value="post"
                className="group relative data-[state=active]:bg-gradient-to-r data-[state=active]:from-gradient-primary-from data-[state=active]:to-gradient-primary-to data-[state=active]:text-white data-[state=inactive]:text-text-secondary dark:data-[state=inactive]:text-text-secondary data-[state=inactive]:hover:text-gradient-primary-from dark:data-[state=inactive]:hover:text-gradient-primary-from rounded-xl py-4 text-base font-semibold transition-all duration-300 flex items-center justify-center gap-2 h-[60px]"
              >
                <div className="flex items-center gap-2">
                  <div className="p-1.5 rounded-lg bg-white/20 group-data-[state=active]:bg-white/30 group-data-[state=inactive]:bg-gradient-primary-from/10 dark:group-data-[state=inactive]:bg-gradient-primary-from/20 transition-all duration-300">
                    <Upload className="h-4 w-4 group-data-[state=inactive]:text-gradient-primary-from" />
                  </div>
                  <div className="flex flex-col items-start">
                    <span className="text-sm font-bold leading-tight">{t("process.postTask")}</span>
                    <span className="text-xs opacity-70 hidden sm:block">Post Tasks</span>
                  </div>
                </div>
              </TabsTrigger>

              <TabsTrigger
                value="complete"
                className="group relative data-[state=active]:bg-gradient-to-r data-[state=active]:from-success data-[state=active]:to-gradient-primary-from data-[state=active]:text-white data-[state=inactive]:text-text-secondary dark:data-[state=inactive]:text-text-secondary data-[state=inactive]:hover:text-success dark:data-[state=inactive]:hover:text-success rounded-xl py-4 text-base font-semibold transition-all duration-300 flex items-center justify-center gap-2 h-[60px]"
              >
                <div className="flex items-center gap-2">
                  <div className="p-1.5 rounded-lg bg-white/20 group-data-[state=active]:bg-white/30 group-data-[state=inactive]:bg-success/10 dark:group-data-[state=inactive]:bg-success/20 transition-all duration-300">
                    <CheckCircle className="h-4 w-4 group-data-[state=inactive]:text-success" />
                  </div>
                  <div className="flex flex-col items-start">
                    <span className="text-sm font-bold leading-tight">{t("process.completeTask")}</span>
                    <span className="text-xs opacity-70 hidden sm:block">Complete Tasks</span>
                  </div>
                </div>
              </TabsTrigger>
            </TabsList>

            {/* Tab切换指示器 - 简化设计 */}
            <motion.div
              initial={{ opacity: 0, y: 10 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.3 }}
              className="mt-4 text-center"
            >
              <div className="inline-flex items-center gap-3 bg-card/80 dark:bg-card/80 backdrop-blur-sm rounded-full px-4 py-2 border border-border/50 dark:border-border/50 shadow-sm">
                <div className={`w-2 h-2 rounded-full transition-all duration-300 ${
                  activeTab === "post"
                    ? "bg-gradient-to-r from-gradient-primary-from to-gradient-primary-to"
                    : "bg-gradient-to-r from-success to-gradient-primary-from"
                }`} />
                <span className="text-xs font-medium text-text-secondary dark:text-text-secondary">
                  {activeTab === "post" ? t("process.postTask") : t("process.completeTask")}
                </span>
                <span className="text-xs text-text-muted dark:text-text-muted">•</span>
                <span className="text-xs text-text-muted dark:text-text-muted">
                  {t("process.stepCount")}
                </span>
              </div>
            </motion.div>
          </div>

          {/* 发布任务流程 */}
          <TabsContent value="post" className="space-y-8">
            <motion.div
              key="post-content"
              initial={{ opacity: 0, x: -20 }}
              animate={{ opacity: 1, x: 0 }}
              exit={{ opacity: 0, x: 20 }}
              transition={{ duration: 0.4, ease: "easeInOut" }}
            >
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 lg:gap-8">
              {postTaskSteps.map((step, index) => {
                const Icon = step.icon;
                return (
                <motion.div
                  key={index}
                  initial={{ opacity: 0, y: 30 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  viewport={{ once: true }}
                  transition={{ duration: 0.6, delay: index * 0.1 }}
                  whileHover={{ scale: 1.05, y: -5 }}
                  onHoverStart={() => setActiveStep(index)}
                  className="relative group h-full"
                >
                  <Card className="h-full min-h-[280px] md:min-h-[320px] bg-card dark:bg-card border-0 shadow-lg hover:shadow-2xl transition-all duration-500 overflow-hidden flex flex-col">
                    <div className={`h-2 bg-gradient-to-r ${step.color} flex-shrink-0`} />
                    <CardHeader className="text-center pb-4 pt-8 flex-grow flex flex-col justify-between">
                      <div className="space-y-4">
                        <div className="relative mb-6">
                          <div className={`mx-auto flex items-center justify-center bg-gradient-to-r ${step.bgColor} dark:from-gradient-primary-from/20 dark:to-gradient-primary-to/20 rounded-2xl w-24 h-24 group-hover:scale-110 transition-all duration-300`}>
                            <Icon className="h-12 w-12 text-text-secondary dark:text-text-secondary" />
                          </div>
                          <div className="absolute -top-2 -right-2 bg-gradient-to-r from-gradient-primary-from to-gradient-primary-to text-white rounded-full w-8 h-8 flex items-center justify-center text-sm font-bold shadow-lg">
                            {index + 1}
                          </div>
                        </div>
                        <CardTitle className="text-xl font-bold text-text-primary dark:text-text-primary mb-3 leading-tight">
                          {step.title}
                        </CardTitle>
                      </div>
                      <div className="space-y-3 mt-auto">
                        <p className="text-text-secondary dark:text-text-secondary leading-relaxed text-base">
                          {step.description}
                        </p>
                        <p className="text-sm text-text-muted dark:text-text-muted font-medium leading-relaxed">
                          {step.details}
                        </p>
                      </div>
                    </CardHeader>
                  </Card>

                  {/* 连接线 - 改进响应式显示 */}
                  {index < postTaskSteps.length - 1 && (
                    <div className="hidden lg:block absolute top-1/2 -right-4 transform -translate-y-1/2 z-10">
                      <div className="bg-card dark:bg-card rounded-full p-1 shadow-sm">
                        <ArrowRight className="h-5 w-5 text-text-muted dark:text-text-muted" />
                      </div>
                    </div>
                  )}
                </motion.div>
              )})}
            </div>
            </motion.div>
          </TabsContent>

          {/* 完成任务流程 */}
          <TabsContent value="complete" className="space-y-8">
            <motion.div
              key="complete-content"
              initial={{ opacity: 0, x: 20 }}
              animate={{ opacity: 1, x: 0 }}
              exit={{ opacity: 0, x: -20 }}
              transition={{ duration: 0.4, ease: "easeInOut" }}
            >
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 lg:gap-8">
              {completeTaskSteps.map((step, index) => {
                const Icon = step.icon;
                return (
                <motion.div
                  key={index}
                  initial={{ opacity: 0, y: 30 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  viewport={{ once: true }}
                  transition={{ duration: 0.6, delay: index * 0.1 }}
                  whileHover={{ scale: 1.05, y: -5 }}
                  onHoverStart={() => setActiveStep(index)}
                  className="relative group h-full"
                >
                  <Card className="h-full min-h-[280px] md:min-h-[320px] bg-card dark:bg-card border-0 shadow-lg hover:shadow-2xl transition-all duration-500 overflow-hidden flex flex-col">
                    <div className={`h-2 bg-gradient-to-r ${step.color} flex-shrink-0`} />
                    <CardHeader className="text-center pb-4 pt-8 flex-grow flex flex-col justify-between">
                      <div className="space-y-4">
                        <div className="relative mb-6">
                          <div className={`mx-auto flex items-center justify-center bg-gradient-to-r ${step.bgColor} dark:from-gradient-primary-from/20 dark:to-gradient-primary-to/20 rounded-2xl w-24 h-24 group-hover:scale-110 transition-all duration-300`}>
                            <Icon className="h-12 w-12 text-text-secondary dark:text-text-secondary" />
                          </div>
                          <div className="absolute -top-2 -right-2 bg-gradient-to-r from-gradient-primary-from to-gradient-primary-to text-white rounded-full w-8 h-8 flex items-center justify-center text-sm font-bold shadow-lg">
                            {index + 1}
                          </div>
                        </div>
                        <CardTitle className="text-xl font-bold text-text-primary dark:text-text-primary mb-3 leading-tight">
                          {step.title}
                        </CardTitle>
                      </div>
                      <div className="space-y-3 mt-auto">
                        <p className="text-text-secondary dark:text-text-secondary leading-relaxed text-base">
                          {step.description}
                        </p>
                        <p className="text-sm text-text-muted dark:text-text-muted font-medium leading-relaxed">
                          {step.details}
                        </p>
                      </div>
                    </CardHeader>
                  </Card>

                  {/* 连接线 - 改进响应式显示 */}
                  {index < completeTaskSteps.length - 1 && (
                    <div className="hidden lg:block absolute top-1/2 -right-4 transform -translate-y-1/2 z-10">
                      <div className="bg-card dark:bg-card rounded-full p-1 shadow-sm">
                        <ArrowRight className="h-5 w-5 text-text-muted dark:text-text-muted" />
                      </div>
                    </div>
                  )}
                </motion.div>
              )})}
            </div>
            </motion.div>
          </TabsContent>
        </Tabs>

        {/* 底部CTA - 改进按钮对比度 */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.8, delay: 0.4 }}
          className="text-center mt-16"
        >
          <div className="bg-gradient-to-r from-gradient-primary-from to-gradient-primary-to rounded-3xl p-8 md:p-12 text-white relative overflow-hidden">
            {/* 背景装饰元素 */}
            <div className="absolute inset-0 bg-gradient-to-r from-gradient-primary-from/90 to-gradient-primary-to/90" />
            <div className="relative z-10">
              <h3 className="text-2xl md:text-3xl font-bold mb-4">
                {t("process.cta.title")}
              </h3>
              <p className="text-xl mb-8 opacity-95">
                {t("process.cta.description")}
              </p>
              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <Button
                  size="lg"
                  className="bg-white text-gradient-primary-from hover:bg-gray-50 hover:text-gradient-primary-from/90 px-8 py-4 text-lg font-semibold shadow-lg border-0 transition-all duration-300 hover:shadow-xl hover:scale-105"
                >
                  <Upload className="h-5 w-5 mr-2" />
                  {t("hero.postTask")}
                </Button>
                <Button
                  size="lg"
                  className="bg-transparent border-2 border-white text-white hover:bg-white hover:text-gradient-primary-from px-8 py-4 text-lg font-semibold transition-all duration-300 hover:shadow-lg hover:scale-105"
                >
                  <Play className="h-5 w-5 mr-2" />
                  {t("process.cta.watchTutorial")}
                </Button>
              </div>
            </div>
          </div>
        </motion.div>
      </div>
    </section>
  );
}
