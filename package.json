{"name": "refundgo-web", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --port 3000", "build": "next build", "start": "next start", "lint": "next lint", "lint:fix": "next lint --fix", "lint:check": "next lint --quiet", "format": "prettier --write .", "format:check": "prettier --check .", "fix": "npm run lint:fix && npm run format", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "test:ci": "jest --ci --coverage --watchAll=false", "test:unit": "jest __tests__/unit", "test:integration": "jest __tests__/integration", "test:validation": "node __tests__/validation/run-validation.js", "test:e2e": "echo 'E2E tests require Playwright. Run: npx playwright test __tests__/e2e'", "prepare": "husky install", "postinstall": "prisma generate", "db:seed": "npx tsx prisma/seed.ts", "db:reset": "npx prisma migrate reset && npm run db:seed", "setup:cron": "node scripts/setup-cron-jobs.js", "test:cron": "node -e \"console.log('Testing cron endpoints...'); ['auto-approve-reviews', 'auto-confirm-delivery', 'expire-tasks', 'expire-membership'].forEach(endpoint => { console.log(`GET http://localhost:3000/api/cron/${endpoint}`); });\""}, "dependencies": {"@auth/prisma-adapter": "^2.9.0", "@hookform/resolvers": "^3.10.0", "@prisma/client": "^6.7.0", "@radix-ui/react-accordion": "^1.1.14", "@radix-ui/react-alert-dialog": "^1.1.14", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-checkbox": "^1.3.2", "@radix-ui/react-collapsible": "^1.1.11", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-popover": "^1.1.14", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-radio-group": "^1.3.7", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.2.5", "@radix-ui/react-tabs": "^1.1.12", "@radix-ui/react-tooltip": "^1.2.7", "@react-three/drei": "^9.122.0", "@react-three/fiber": "^8.18.0", "@tailwindcss/typography": "^0.5.16", "@tanstack/react-query": "^5.75.5", "@types/bcryptjs": "^2.4.6", "axios": "^1.9.0", "bcryptjs": "^3.0.2", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "currency.js": "^2.0.4", "date-fns": "^4.1.0", "framer-motion": "latest", "lucide-react": "^0.508.0", "next": "^14.2.16", "next-auth": "^5.0.0-beta.29", "next-intl": "^4.3.4", "next-themes": "^0.4.6", "nextjs-toploader": "^3.8.16", "react": "^18.2.0", "react-dom": "^18.2.0", "react-hook-form": "^7.58.1", "react-icons": "^5.5.0", "resend": "^4.5.1", "sonner": "^2.0.5", "tailwind-merge": "^3.2.0", "tailwindcss-animate": "^1.0.7", "three": "^0.160.1", "vaul": "^1.1.2", "zod": "^3.25.67"}, "devDependencies": {"@eslint/eslintrc": "^3.3.1", "@eslint/js": "^9.31.0", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^14.6.1", "@types/jest": "^30.0.0", "@types/node": "^24.0.4", "@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "@types/three": "^0.178.1", "eslint": "8.57.1", "eslint-config-next": "15.3.5", "eslint-config-prettier": "^10.1.5", "eslint-import-resolver-typescript": "^4.4.4", "eslint-plugin-import": "^2.32.0", "husky": "^9.0.11", "identity-obj-proxy": "^3.0.0", "jest": "^30.0.4", "jest-environment-jsdom": "^30.0.4", "lint-staged": "^15.2.10", "postcss": "^8", "prettier": "^3.2.5", "prisma": "^6.7.0", "tailwindcss": "^3", "ts-node": "^10.9.2", "tsx": "^4.20.3", "typescript": "^5.8.3", "typescript-eslint": "^8.36.0"}, "lint-staged": {"*.{js,jsx,ts,tsx}": ["eslint --fix", "prettier --write"]}}