"use client";

import { ArrowRight } from "lucide-react";
import { useTranslations } from "next-intl";
import { useState } from "react";

import { Button } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";

export function ProcessSection() {
  const [activeTab, setActiveTab] = useState<"post" | "complete">("post");
  const t = useTranslations("HomePage");

  const postTaskSteps = [
    {
      title: t("process.publish.steps.post.title"),
      description: t("process.publish.steps.post.description"),
      step: 1,
    },
    {
      title: t("process.publish.steps.review.title"),
      description: t("process.publish.steps.review.description"),
      step: 2,
    },
    {
      title: t("process.publish.steps.accept.title"),
      description: t("process.publish.steps.accept.description"),
      step: 3,
    },
    {
      title: t("process.publish.steps.settle.title"),
      description: t("process.publish.steps.settle.description"),
      step: 4,
    },
  ];

  const completeTaskSteps = [
    {
      title: t("process.complete.steps.browse.title"),
      description: t("process.complete.steps.browse.description"),
      step: 1,
    },
    {
      title: t("process.complete.steps.accept.title"),
      description: t("process.complete.steps.accept.description"),
      step: 2,
    },
    {
      title: t("process.complete.steps.execute.title"),
      description: t("process.complete.steps.execute.description"),
      step: 3,
    },
    {
      title: t("process.complete.steps.reward.title"),
      description: t("process.complete.steps.reward.description"),
      step: 4,
    },
  ];

  const currentSteps = activeTab === "post" ? postTaskSteps : completeTaskSteps;

  return (
    <section id="process" className="py-20 bg-white">
      <div className="container mx-auto px-4">
        {/* Section Header */}
        <div className="text-center mb-16">
          <h2 className="text-4xl md:text-5xl font-bold text-gray-900 mb-4">{t("process.title")}</h2>
        </div>

        {/* Tab Navigation */}
        <div className="flex justify-center mb-12">
          <div className="bg-gray-100 p-1 rounded-lg">
            <Button
              variant={activeTab === "post" ? "default" : "ghost"}
              onClick={() => setActiveTab("post")}
              className="px-8 py-3"
            >
              {t("process.postTask")}
            </Button>
            <Button
              variant={activeTab === "complete" ? "default" : "ghost"}
              onClick={() => setActiveTab("complete")}
              className="px-8 py-3"
            >
              {t("process.completeTask")}
            </Button>
          </div>
        </div>

        {/* Process Steps */}
        <div className="max-w-6xl mx-auto">
          <div className="grid md:grid-cols-4 gap-8">
            {currentSteps.map((step, index) => (
              <div key={index} className="relative">
                {/* Connection Line */}
                {index < currentSteps.length - 1 && (
                  <div className="hidden md:block absolute top-12 left-full w-full h-0.5 bg-gray-200 z-0">
                    <div className="absolute right-0 top-1/2 transform -translate-y-1/2">
                      <ArrowRight className="w-4 h-4 text-gray-400" />
                    </div>
                  </div>
                )}

                <Card className="relative z-10 hover:shadow-lg transition-shadow duration-300">
                  <CardContent className="p-6 text-center">
                    <div className="inline-flex items-center justify-center w-12 h-12 bg-blue-600 text-white rounded-full font-bold text-lg mb-4">
                      {step.step}
                    </div>
                    <h3 className="text-xl font-bold text-gray-900 mb-2">{step.title}</h3>
                    <p className="text-gray-600">{step.description}</p>
                  </CardContent>
                </Card>
              </div>
            ))}
          </div>
        </div>
      </div>
    </section>
  );
}
