{
    "cSpell.words": [
        "chargeback",
        "refundgo",
        "relogin"
    ],
    // i18n Ally configuration for next-intl
    "i18n-ally.localesPaths": [
        "messages"
    ],
    "i18n-ally.keystyle": "nested",
    "i18n-ally.pathMatcher": "{locale}/{namespaces}.json",
    "i18n-ally.namespace": true,
    "i18n-ally.enabledParsers": ["json"],
    "i18n-ally.sourceLanguage": "zh",
    "i18n-ally.displayLanguage": "en",
    "i18n-ally.enabledFrameworks": ["next-intl", "react"],
    "i18n-ally.extract.autoDetect": true,
    "i18n-ally.extract.keygenStyle": "camelCase"
}