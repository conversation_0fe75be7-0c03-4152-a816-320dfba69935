import { CurrencyConverter } from '../currency-converter';

describe('CurrencyConverter', () => {
  let converter: CurrencyConverter;

  beforeEach(() => {
    converter = new CurrencyConverter();
    // 清除缓存以确保每个测试都是独立的
    converter.clearCache();
  });

  describe('convertUSDToCNY', () => {
    it('should convert USD to CNY successfully', async () => {
      const result = await converter.convertUSDToCNY(100);

      expect(result.originalAmount).toBe(100);
      expect(result.originalCurrency).toBe('USD');
      expect(result.convertedCurrency).toBe('CNY');
      expect(result.convertedAmount).toBeGreaterThan(0);
      expect(result.exchangeRate).toBeGreaterThan(0);
      expect(result.source).toBeDefined();
    }, 10000); // 10秒超时，因为需要网络请求

    it('should handle same currency conversion', async () => {
      const result = await converter.convertCurrency(100, 'USD', 'USD');

      expect(result.originalAmount).toBe(100);
      expect(result.originalCurrency).toBe('USD');
      expect(result.convertedAmount).toBe(100);
      expect(result.convertedCurrency).toBe('USD');
      expect(result.exchangeRate).toBe(1);
      expect(result.source).toBe('same-currency');
    });

    it('should use cached exchange rate on second call', async () => {
      // 第一次调用
      const result1 = await converter.convertUSDToCNY(100);

      // 第二次调用应该使用缓存
      const result2 = await converter.convertUSDToCNY(200);

      expect(result1.exchangeRate).toBe(result2.exchangeRate);
      expect(result1.source).toBe(result2.source);
      expect(result2.convertedAmount).toBe(result1.convertedAmount * 2);
    }, 10000);

    it('should handle network errors gracefully with fallback rate', async () => {
      // 创建一个会失败的转换器来测试降级
      const failingConverter = new CurrencyConverter();

      // 模拟网络错误 - 这里我们依赖降级汇率
      try {
        const result = await failingConverter.convertCurrency(
          100,
          'USD',
          'CNY',
        );

        // 如果成功，应该有有效的结果
        expect(result.originalAmount).toBe(100);
        expect(result.originalCurrency).toBe('USD');
        expect(result.convertedCurrency).toBe('CNY');
        expect(result.convertedAmount).toBeGreaterThan(0);
      } catch (error) {
        // 如果所有API都失败且没有降级汇率，应该抛出错误
        expect(error).toBeInstanceOf(Error);
      }
    }, 15000);
  });

  describe('cache management', () => {
    it('should cache exchange rates', async () => {
      await converter.convertUSDToCNY(100);

      const cacheInfo = converter.getCacheInfo();
      expect(cacheInfo.length).toBeGreaterThan(0);

      const usdCnyCache = cacheInfo.find(item => item.key === 'USD-CNY');
      expect(usdCnyCache).toBeDefined();
      expect(usdCnyCache?.rate.rate).toBeGreaterThan(0);
    }, 10000);

    it('should clear cache', async () => {
      await converter.convertUSDToCNY(100);
      expect(converter.getCacheInfo().length).toBeGreaterThan(0);

      converter.clearCache();
      expect(converter.getCacheInfo().length).toBe(0);
    }, 10000);
  });

  describe('precision handling', () => {
    it('should handle decimal amounts correctly', async () => {
      const result = await converter.convertUSDToCNY(123.45);

      expect(result.originalAmount).toBe(123.45);
      expect(result.convertedAmount).toBeGreaterThan(0);
      // 检查精度 - 应该保留2位小数
      expect(Number(result.convertedAmount.toFixed(2))).toBe(
        result.convertedAmount,
      );
    }, 10000);

    it('should handle very small amounts', async () => {
      const result = await converter.convertUSDToCNY(0.01);

      expect(result.originalAmount).toBe(0.01);
      expect(result.convertedAmount).toBeGreaterThan(0);
    }, 10000);

    it('should handle large amounts', async () => {
      const result = await converter.convertUSDToCNY(999999.99);

      expect(result.originalAmount).toBe(999999.99);
      expect(result.convertedAmount).toBeGreaterThan(0);
    }, 10000);
  });
});
