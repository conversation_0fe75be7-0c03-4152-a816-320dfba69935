import createNextIntlPlugin from 'next-intl/plugin';

const withNextIntl = createNextIntlPlugin();

/** @type {import('next').NextConfig} */
const nextConfig = {
  // 移除 i18n 配置，App Router 使用手动国际化
  images: {
    // 本地图片
    domains: ['localhost', '127.0.0.1'],
    // 允许所有远程图片
    remotePatterns: [
      {
        protocol: 'https',
        hostname: '**',
      },
      {
        protocol: 'http',
        hostname: '**',
      },
    ],
    // 输出格式
    formats: ['image/webp', 'image/avif'],
    // 尺寸配置
    imageSizes: [16, 32, 48, 64, 96, 128, 256, 384],
    deviceSizes: [640, 750, 828, 1080, 1200, 1920, 2048, 3840],
    // 支持SVG
    dangerouslyAllowSVG: true,
    contentSecurityPolicy: "default-src 'self'; script-src 'none'; sandbox;",
    unoptimized: false,
  },
  // 实验性功能
  experimental: {
    // 移除过时配置
  },
  // 日志配置
  logging: {
    fetches: {
      fullUrl: false,
    },
  },
  // 构建ID
  generateBuildId: async () => {
    return null;
  },
};

export default withNextIntl(nextConfig);
