import { emailStyles } from '@/hooks/useEmailTranslation';
import { getEmailTranslations, type SupportedLanguage } from '@/lib/email-translations';
import {
  formatEmailAmount,
  formatEmailDateTime,
  formatTransactionId
} from '@/lib/email-formatting';

export interface DepositSuccessEmailData {
  userName: string;
  userEmail: string;
  amount: number;
  currency: string;
  transactionId: string;
  paymentMethod: string;
  processedAt: string;
  newBalance: number;
  language?: SupportedLanguage;
}

export const depositSuccessTemplateI18n = (
  data: DepositSuccessEmailData
): string => {
  const language = data.language || 'zh';
  const langAttr = language === 'en' ? 'en' : 'zh-CN';
  const t = getEmailTranslations(language);

  // Format data using centralized formatting utilities
  const formattedDate = formatEmailDateTime(data.processedAt, language, { includeTime: true });
  const formattedAmount = formatEmailAmount(data.amount, data.currency, language);
  const formattedBalance = formatEmailAmount(data.newBalance, data.currency, language);
  const maskedTransactionId = formatTransactionId(data.transactionId);




  return `
    <!DOCTYPE html>
    <html lang="${langAttr}">
    <head>
      <meta charset="UTF-8">
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <title>${t.notifications.financial.depositSuccess.title} - ${t.common.brandName}</title>
      <style>
        ${emailStyles}
        .success-icon {
          width: 64px;
          height: 64px;
          background: linear-gradient(135deg, #10b981, #059669);
          border-radius: 50%;
          display: flex;
          align-items: center;
          justify-content: center;
          margin: 0 auto 24px;
        }
        .amount-highlight {
          font-size: 28px;
          font-weight: bold;
          color: #10b981;
          text-align: center;
          margin: 16px 0;
        }
        .transaction-card {
          background: #f8fafc;
          border: 1px solid #e2e8f0;
          border-radius: 8px;
          padding: 20px;
          margin: 20px 0;
        }
        .info-row {
          display: flex;
          justify-content: space-between;
          align-items: center;
          padding: 8px 0;
          border-bottom: 1px solid #e2e8f0;
        }
        .info-row:last-child {
          border-bottom: none;
        }
        .info-label {
          color: #64748b;
          font-weight: 500;
        }
        .info-value {
          color: #1e293b;
          font-weight: 600;
        }
        .balance-card {
          background: linear-gradient(135deg, #3b82f6, #1d4ed8);
          color: white;
          border-radius: 8px;
          padding: 20px;
          text-align: center;
          margin: 20px 0;
        }
        .steps-list {
          list-style: none;
          padding: 0;
          margin: 16px 0;
        }
        .steps-list li {
          padding: 8px 0;
          padding-left: 24px;
          position: relative;
        }
        .steps-list li:before {
          content: "✓";
          position: absolute;
          left: 0;
          color: #10b981;
          font-weight: bold;
        }
        .security-notice {
          background: #fef3c7;
          border: 1px solid #f59e0b;
          border-radius: 8px;
          padding: 16px;
          margin: 20px 0;
        }
        .security-notice .icon {
          color: #f59e0b;
          font-size: 18px;
          margin-right: 8px;
        }
      </style>
    </head>
    <body>
      <div class="email-container">
        <!-- Header -->
        <div class="email-header">
          <h1>${t.common.brandName}</h1>
        </div>

        <!-- Content -->
        <div class="email-content">
          <!-- Success Icon -->
          <div class="success-icon">
            <svg width="32" height="32" fill="white" viewBox="0 0 24 24">
              <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"/>
            </svg>
          </div>

          <!-- Greeting -->
          <h2 style="text-align: center; color: #1e293b; margin-bottom: 8px;">
            ${t.common.greeting}, ${data.userName}！
          </h2>
          
          <h3 style="text-align: center; color: #10b981; margin-bottom: 16px;">
            ${t.notifications.financial.depositSuccess.header}
          </h3>

          <p style="text-align: center; color: #64748b; margin-bottom: 24px;">
            ${t.notifications.financial.depositSuccess.description}
          </p>

          <!-- Amount Highlight -->
          <div class="amount-highlight">
            ${formattedAmount}
          </div>

          <!-- Transaction Details -->
          <div class="transaction-card">
            <h4 style="margin-top: 0; color: #1e293b;">
              Transaction Details
            </h4>

            <div class="info-row">
              <span class="info-label">${t.notifications.financial.depositSuccess.amountLabel}</span>
              <span class="info-value">${formattedAmount}</span>
            </div>

            <div class="info-row">
              <span class="info-label">${t.notifications.financial.depositSuccess.paymentMethodLabel}</span>
              <span class="info-value">${data.paymentMethod}</span>
            </div>

            <div class="info-row">
              <span class="info-label">${t.notifications.financial.depositSuccess.transactionIdLabel}</span>
              <span class="info-value" style="font-family: monospace;">${maskedTransactionId}</span>
            </div>

            <div class="info-row">
              <span class="info-label">${t.notifications.financial.depositSuccess.processedAtLabel}</span>
              <span class="info-value">${formattedDate}</span>
            </div>
          </div>

          <!-- Account Balance -->
          <div class="balance-card">
            <h4 style="margin-top: 0; margin-bottom: 8px;">
              Account Information
            </h4>
            <div style="font-size: 24px; font-weight: bold;">
              ${t.notifications.financial.depositSuccess.newBalanceLabel} ${formattedBalance}
            </div>
          </div>

          <!-- Success Message -->
          <div class="success-box">
            <p style="margin: 0; color: #1e293b; text-align: center;">
              <strong>🎉 ${language === 'zh' ? '充值成功！' : 'Deposit Successful!'}</strong><br>
              ${language === 'zh' ? '感谢您选择RefundGo！' : 'Thank you for choosing RefundGo!'}
            </p>
          </div>

          <!-- Action Buttons -->
          <div class="email-buttons">
            <a href="${process.env.DOMAIN}/dashboard/wallet" class="email-button email-button-primary">
              ${t.ui.buttons.checkAccount}
            </a>
            <a href="${process.env.DOMAIN}/support" class="email-button email-button-secondary">
              ${t.ui.buttons.contactSupport}
            </a>
          </div>


        </div>

        <!-- Footer -->
        <div class="email-footer">
          <p>${t.common.regards},<br>${t.common.team}</p>
          <div class="email-footer-links">
            <p style="margin: 8px 0; color: #64748b; font-size: 12px;">
              ${t.common.autoMessage}
            </p>
            <p style="margin: 8px 0; color: #64748b; font-size: 12px;">
              ${t.common.copyright}
            </p>
          </div>
        </div>
      </div>
    </body>
    </html>
  `;
};
