'use client';

import { useQuery } from '@tanstack/react-query';
import {
  Wallet,
  Plus,
  Minus,
  TrendingUp,
  TrendingDown,
  Clock,
  CheckCircle,
  XCircle,
  AlertCircle,
  CreditCard,
  Building,
  DollarSign,
  Bitcoin,
  Shield,
  MessageCircle,
} from 'lucide-react';
import { useTranslations, useLocale } from 'next-intl';
import { useState, useMemo, useEffect } from 'react';
import { toast } from 'sonner';

import { useResponsive } from '@/hooks/use-responsive';

import { BankWithdrawalModal } from '@/components/bank-withdrawal-modal';
import { CryptoWithdrawalModal } from '@/components/crypto-withdrawal-modal';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { usePaymentMethods } from '@/hooks/use-payment-methods';
import { useUserSystemRate } from '@/hooks/use-rates-api';
import {
  useUserWallet,
  useWalletTransactions,
  useDeposit,
  useWithdraw,
} from '@/hooks/use-wallet';
import { TransactionType, TransactionStatus } from '@/lib/types/membership';

import { WithdrawalVerificationDialog } from './withdrawal-verification-dialog';

// Transaction List Component
interface TransactionListProps {
  transactions: any[];
  t: any;
  tTransactions: any;
  formatDateTime: (date: Date) => string;
  translateTransactionDescription: (description: string, type: TransactionType) => string;
  getTypeIcon: (type: TransactionType) => React.ReactNode;
  getStatusColor: (status: TransactionStatus) => string;
  getStatusIcon: (status: TransactionStatus) => React.ReactNode;
}

function TransactionList({
  transactions,
  t,
  tTransactions,
  formatDateTime,
  translateTransactionDescription,
  getTypeIcon,
  getStatusColor,
  getStatusIcon,
}: TransactionListProps) {
  if (transactions.length === 0) {
    return (
      <div className='text-center py-8 text-muted-foreground'>
        {t('transactions.empty')}
      </div>
    );
  }

  return (
    <div className='space-y-4'>
      {transactions.map(transaction => (
        <div
          key={transaction.id}
          className='border rounded-lg overflow-hidden p-3 md:p-4'
        >
          {/* Responsive Layout using CSS classes */}
          <div className='space-y-3 md:space-y-0 md:flex md:items-start md:gap-4'>
            {/* Mobile: Stacked Layout, Desktop: Left Section */}
            <div className='flex-1 min-w-0'>
              {/* Header Row - Mobile: Icon + Type + Amount + Status, Desktop: Icon + Details */}
              <div className='flex items-center justify-between md:justify-start md:gap-3'>
                <div className='flex items-center gap-2 flex-1 min-w-0 md:flex-initial'>
                  <div className='flex-shrink-0'>
                    {getTypeIcon(transaction.type)}
                  </div>
                  <div className='flex-1 min-w-0 md:flex-initial'>
                    {/* Mobile: Show type, Desktop: Show description */}
                    <div className='text-xs text-muted-foreground truncate md:hidden'>
                      {tTransactions(`types.${transaction.type}`)}
                    </div>
                    <div className='hidden md:block font-medium break-words'>
                      {translateTransactionDescription(
                        transaction.description,
                        transaction.type,
                      )}
                    </div>
                  </div>
                </div>

                {/* Mobile: Amount + Status (shown here), Desktop: Hidden (shown in right section) */}
                <div className='flex items-center gap-2 flex-shrink-0 md:hidden'>
                  <div
                    className={`font-semibold text-sm ${
                      transaction.amount > 0 ? 'text-green-600' : 'text-red-600'
                    }`}
                  >
                    {transaction.amount > 0 ? '+' : ''}$
                    {Math.abs(transaction.amount).toFixed(2)}
                  </div>
                  <Badge className={`${getStatusColor(transaction.status)} text-xs px-2 py-1`}>
                    {getStatusIcon(transaction.status)}
                    <span className='ml-1'>
                      {tTransactions(`status.${transaction.status}`)}
                    </span>
                  </Badge>
                </div>
              </div>

              {/* Mobile: Description Row, Desktop: Date + Reference */}
              <div className='mt-3 space-y-1 md:mt-1'>
                {/* Mobile: Show description */}
                <div className='text-sm font-medium break-words md:hidden'>
                  {translateTransactionDescription(
                    transaction.description,
                    transaction.type,
                  )}
                </div>

                {/* Date - shown on both mobile and desktop */}
                <div className='text-xs md:text-sm text-muted-foreground'>
                  {formatDateTime(transaction.createdAt)}
                </div>

                {/* Reference - different layout for mobile vs desktop */}
                {transaction.reference && (
                  <div className='md:text-xs md:text-muted-foreground'>
                    {/* Mobile: Separate section with border */}
                    <div className='pt-2 border-t border-border/50 md:pt-0 md:border-t-0'>
                      <div className='text-xs text-muted-foreground mb-1 md:mb-0 md:inline-block md:mr-1'>
                        {t('transactions.reference')}：
                      </div>
                      <div className='text-xs font-mono bg-muted/50 md:bg-muted/30 p-2 md:px-2 md:py-1 rounded text-muted-foreground break-all md:inline-block'>
                        {transaction.reference}
                      </div>
                    </div>
                  </div>
                )}
              </div>
            </div>

            {/* Desktop: Right Section - Amount and Status */}
            <div className='hidden md:flex md:items-center md:gap-3 md:flex-shrink-0'>
              <div className='text-right'>
                <div
                  className={`font-semibold ${
                    transaction.amount > 0 ? 'text-green-600' : 'text-red-600'
                  }`}
                >
                  {transaction.amount > 0 ? '+' : ''}$
                  {Math.abs(transaction.amount).toFixed(2)}
                </div>
                <div className='text-sm text-muted-foreground'>
                  {tTransactions(`types.${transaction.type}`)}
                </div>
              </div>

              <Badge className={getStatusColor(transaction.status)}>
                {getStatusIcon(transaction.status)}
                <span className='ml-1'>
                  {tTransactions(`status.${transaction.status}`)}
                </span>
              </Badge>
            </div>
          </div>
        </div>
      ))}
    </div>
  );
}

// Responsive Transaction History Tabs Component
function ResponsiveTransactionTabs({
  activeTab,
  onTabChange,
  t,
  children
}: {
  activeTab: string;
  onTabChange: (value: string) => void;
  t: any;
  children: React.ReactNode;
}) {
  const { isMobile, isTablet } = useResponsive();

  const tabItems = [
    { value: 'overview', label: t('transactions.all'), shortLabel: t('transactions.all') },
    { value: 'deposit', label: t('transactions.deposit'), shortLabel: t('transactions.deposit') },
    { value: 'withdraw', label: t('transactions.withdraw'), shortLabel: t('transactions.withdraw') },
    { value: 'income', label: t('transactions.income'), shortLabel: t('transactions.income') },
    { value: 'expense', label: t('transactions.expense'), shortLabel: t('transactions.expense') },
  ];

  // Mobile: Use horizontal scrolling
  if (isMobile) {
    return (
      <Tabs value={activeTab} onValueChange={onTabChange} className='w-full'>
        <div className='w-full overflow-x-auto scrollbar-hide responsive-tabs-mobile pb-1'>
          <TabsList className='inline-flex h-10 items-center justify-start rounded-lg bg-muted p-1 text-muted-foreground min-w-max gap-1'>
            {tabItems.map(item => (
              <TabsTrigger
                key={item.value}
                value={item.value}
                className='whitespace-nowrap px-2 py-1.5 text-xs font-medium min-w-[60px] flex-shrink-0 touch-manipulation responsive-tab-trigger'
              >
                <span className='block xs:hidden'>{item.shortLabel}</span>
                <span className='hidden xs:block'>{item.label}</span>
              </TabsTrigger>
            ))}
          </TabsList>
        </div>
        {children}
      </Tabs>
    );
  }

  // Tablet: Use 3x2 grid layout
  if (isTablet) {
    return (
      <Tabs value={activeTab} onValueChange={onTabChange} className='w-full'>
        <div className='grid grid-cols-3 gap-2'>
          {tabItems.map(item => (
            <button
              key={item.value}
              type='button'
              onClick={() => onTabChange(item.value)}
              className={`h-10 px-3 py-2 text-sm font-medium rounded-md border transition-colors touch-manipulation responsive-tab-trigger ${
                activeTab === item.value
                  ? 'bg-primary text-primary-foreground border-primary'
                  : 'border-border bg-background hover:bg-accent hover:text-accent-foreground'
              }`}
            >
              {item.label}
            </button>
          ))}
        </div>
        {children}
      </Tabs>
    );
  }

  // Desktop: Keep original 5-column grid layout
  return (
    <Tabs value={activeTab} onValueChange={onTabChange} className='w-full'>
      <TabsList className='grid w-full grid-cols-5'>
        {tabItems.map(item => (
          <TabsTrigger key={item.value} value={item.value}>
            {item.label}
          </TabsTrigger>
        ))}
      </TabsList>
      {children}
    </Tabs>
  );
}

export function WalletContent() {
  const t = useTranslations('Wallet');
  const tTransactions = useTranslations('transactions');
  const locale = useLocale();

  const [mounted, setMounted] = useState(false);
  const [activeTab, setActiveTab] = useState('overview');
  const [depositAmount, setDepositAmount] = useState('');
  const [depositMethod, setDepositMethod] = useState<string>('');
  const [withdrawAmount, setWithdrawAmount] = useState('');
  const [withdrawMethod, setWithdrawMethod] = useState<string>('');

  // 模态框状态
  const [isBankWithdrawModalOpen, setIsBankWithdrawModalOpen] = useState(false);
  const [isCryptoWithdrawModalOpen, setIsCryptoWithdrawModalOpen] =
    useState(false);

  // 邮箱验证相关状态
  const [isVerificationDialogOpen, setIsVerificationDialogOpen] =
    useState(false);
  const [isEmailVerified, setIsEmailVerified] = useState(false); // 新增：邮箱验证状态
  const [verificationToken, setVerificationToken] = useState<string>(''); // 新增：验证令牌
  const [pendingWithdrawalData, setPendingWithdrawalData] = useState<{
    amount: string;
    method: string;
  } | null>(null);

  // 使用真实数据hooks
  const {
    data: wallet,
    isLoading: walletLoading,
    error: walletError,
  } = useUserWallet();
  const {
    data: transactionData,
    isLoading: transactionsLoading,
    error: transactionsError,
  } = useWalletTransactions();
  const { data: paymentMethods, isLoading: paymentMethodsLoading } =
    usePaymentMethods();
  const { data: systemRate, isLoading: systemRateLoading } =
    useUserSystemRate();
  const depositMutation = useDeposit();
  const withdrawMutation = useWithdraw();

  useEffect(() => {
    setMounted(true);
  }, []);

  // 设置默认支付方式
  useEffect(() => {
    if (paymentMethods?.methods && paymentMethods.methods.length > 0) {
      const defaultMethod =
        paymentMethods.methods.find(m => m.id === 'alipay') ||
        paymentMethods.methods[0];
      if (!depositMethod) {
        setDepositMethod(defaultMethod.id);
      }
    }

    // 设置默认提现方式
    if (!withdrawMethod) {
      setWithdrawMethod('BANK_CARD');
    }
  }, [paymentMethods, depositMethod, withdrawMethod]);

  // 筛选交易记录
  const filteredTransactions = useMemo(() => {
    const transactions = transactionData?.transactions || [];
    switch (activeTab) {
      case 'deposit':
        return transactions.filter(tx => tx.type === TransactionType.DEPOSIT);
      case 'withdraw':
        return transactions.filter(tx => tx.type === TransactionType.WITHDRAW);
      case 'income':
        return transactions.filter(tx => tx.amount > 0);
      case 'expense':
        return transactions.filter(tx => tx.amount < 0);
      default:
        return transactions;
    }
  }, [transactionData?.transactions, activeTab]);

  // 获取交易状态颜色
  const getStatusColor = (status: TransactionStatus) => {
    switch (status) {
      case TransactionStatus.COMPLETED:
        return 'bg-green-100 text-green-800 border-green-200 dark:bg-green-900/30 dark:text-green-100 dark:border-green-600';
      case TransactionStatus.PENDING:
        return 'bg-yellow-100 text-yellow-800 border-yellow-200 dark:bg-yellow-900/30 dark:text-yellow-100 dark:border-yellow-600';
      case TransactionStatus.FAILED:
        return 'bg-red-100 text-red-800 border-red-200 dark:bg-red-900/30 dark:text-red-100 dark:border-red-600';
      case TransactionStatus.CANCELLED:
        return 'bg-gray-100 text-gray-800 border-gray-200';
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  // 获取交易状态图标
  const getStatusIcon = (status: TransactionStatus) => {
    switch (status) {
      case TransactionStatus.COMPLETED:
        return <CheckCircle className='h-4 w-4' />;
      case TransactionStatus.PENDING:
        return <Clock className='h-4 w-4' />;
      case TransactionStatus.FAILED:
        return <XCircle className='h-4 w-4' />;
      case TransactionStatus.CANCELLED:
        return <AlertCircle className='h-4 w-4' />;
      default:
        return <Clock className='h-4 w-4' />;
    }
  };

  // 获取交易类型图标
  const getTypeIcon = (type: TransactionType) => {
    switch (type) {
      case TransactionType.DEPOSIT:
        return <Plus className='h-4 w-4 text-green-600' />;
      case TransactionType.WITHDRAW:
        return <Minus className='h-4 w-4 text-red-600' />;
      case TransactionType.COMMISSION:
        return <TrendingUp className='h-4 w-4 text-green-600' />;
      case TransactionType.TASK_FEE:
        return <TrendingDown className='h-4 w-4 text-red-600' />;
      case TransactionType.MEMBERSHIP:
        return <TrendingDown className='h-4 w-4 text-orange-600' />;
      case TransactionType.REFUND:
        return <Plus className='h-4 w-4 text-blue-600' />;
      default:
        return <DollarSign className='h-4 w-4' />;
    }
  };

  // 获取支付方式图标
  const getPaymentIcon = (method: string) => {
    switch (method) {
      case 'alipay':
        return <Wallet className='h-4 w-4' />;
      case 'wechat':
      case 'wxpay':
        return <MessageCircle className='h-4 w-4' />;
      case 'bank_card':
        return <Building className='h-4 w-4' />;
      case 'paypal':
        return <CreditCard className='h-4 w-4' />;
      case 'crypto':
        return <Bitcoin className='h-4 w-4' />;
      default:
        return <CreditCard className='h-4 w-4' />;
    }
  };

  // 格式化日期时间
  const formatDateTime = (date: Date) => {
    if (!mounted) return t('messages.loading');

    // 根据当前语言设置格式化日期时间
    const localeCode = locale === 'zh' ? 'zh-CN' : 'en-US';
    return date.toLocaleString(localeCode, {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  // 通用词汇翻译函数
  const translateCommonTerms = (text: string): string => {
    if (!text) return text;

    let translatedText = text;

    // 定义通用词汇翻译映射
    const termTranslations: Record<string, string> = {
      // 费用相关
      手续费: 'Fee',
      证据费: 'Evidence Fee',
      发布费用: 'Publishing Fee',
      押金: 'Deposit',
      酬金: 'Commission',
      违约金: 'Penalty',
      奖励金: 'Bonus',

      // 平台相关
      独立站: 'Independent Site',
      DHgate: 'DHgate',

      // 支付方式相关
      美元账户: 'USD Account',
      银行卡: 'Bank Card',
      银行账户: 'Bank Account',
      支付宝: 'Alipay',
      微信支付: 'WeChat Pay',

      // 状态相关
      冻结中: 'Frozen',
      已完成: 'Completed',
      处理中: 'Processing',
      已取消: 'Cancelled',
      已拒绝: 'Rejected',
      已通过: 'Approved',
      失败: 'Failed',
      成功: 'Success',
      冻结: 'Frozen',

      // 错误信息相关
      银行卡信息错误: 'Bank card information error',
      银行信息错误: 'Bank information error',
      收款信息错误: 'Payment information error',
      账户信息不匹配: 'Account information mismatch',
      网络超时: 'Network timeout',
      系统维护中: 'System maintenance',
      未提供原因: 'No reason provided',

      // 操作相关
      充值: 'Deposit',
      提现: 'Withdrawal',
      退款: 'Refund',
      转账: 'Transfer',
      扣除: 'Deduction',
      释放: 'Release',
      解冻: 'Unfreeze',

      // 委托相关
      委托完成: 'Task Completed',
      委托: 'Task',
      接单: 'Accept',
      发布: 'Publish',
      完成: 'Complete',
      取消: 'Cancel',
      过期: 'Expired',
      审核: 'Review',
      证据: 'Evidence',
      确认收货: 'Confirm Delivery',
      自动确认: 'Auto Confirm',

      // 会员相关
      会员: 'Membership',
      升级: 'Upgrade',
      续费: 'Renewal',
      套餐: 'Plan',

      // 其他常用词
      申请: 'Application',
      被拒绝: 'Rejected',
      余额已返还: 'Balance Refunded',
      转为: 'Converted to',
      获得: 'Earned',
      补偿: 'Compensation',
      违约: 'Violation',
      放弃: 'Abandon',
      超时: 'Timeout',
      到账: 'Received',
    };

    // 按词汇长度排序，优先匹配长词汇
    const sortedTerms = Object.keys(termTranslations).sort(
      (a, b) => b.length - a.length,
    );

    // 逐个替换词汇，保持词汇间的空格
    for (const chineseTerm of sortedTerms) {
      const englishTerm = termTranslations[chineseTerm];
      // 使用全局替换，但保持原有的分隔符
      translatedText = translatedText.replace(
        new RegExp(chineseTerm, 'g'),
        englishTerm,
      );
    }

    // 后处理：修复可能的词汇连接问题
    translatedText = translatedText
      // 在中英文之间添加空格
      .replace(/([a-zA-Z])([一-龯])/g, '$1 $2')
      .replace(/([一-龯])([a-zA-Z])/g, '$1 $2')
      // 修复常见的词汇连接问题
      .replace(/TaskDeposit/g, 'Task Deposit')
      .replace(/TaskEvidence/g, 'Task Evidence')
      .replace(/TaskPublishing/g, 'Task Publishing')
      .replace(/TaskComplete/g, 'Task Complete')
      .replace(/TaskAcceptance/g, 'Task Acceptance')
      .replace(/TaskExpired/g, 'Task Expired')
      .replace(/TaskCancellation/g, 'Task Cancellation')
      .replace(/TaskReview/g, 'Task Review')
      .replace(/TaskAuto/g, 'Task Auto')
      .replace(/TaskDelivery/g, 'Task Delivery')
      .replace(/TaskTimeout/g, 'Task Timeout')
      .replace(/TaskAbandonment/g, 'Task Abandonment')
      .replace(/BankCard/g, 'Bank Card')
      .replace(/BankAccount/g, 'Bank Account')
      .replace(/WeChatPay/g, 'WeChat Pay')
      .replace(/USDAccount/g, 'USD Account')
      .replace(/EvidenceFee/g, 'Evidence Fee')
      .replace(/PublishingFee/g, 'Publishing Fee')
      .replace(/MembershipPlan/g, 'Membership Plan')
      .replace(/MembershipUpgrade/g, 'Membership Upgrade')
      .replace(/MembershipRenewal/g, 'Membership Renewal')
      .replace(/BonusReward/g, 'Bonus Reward')
      .replace(/DepositFreeze/g, 'Deposit Frozen')
      .replace(/DepositRelease/g, 'Deposit Release')
      .replace(/DepositDeduction/g, 'Deposit Deduction')
      .replace(/DepositRefund/g, 'Deposit Refund')
      .replace(/CommissionPaid/g, 'Commission Paid')
      .replace(/CommissionEarned/g, 'Commission Earned')
      .replace(/BalanceRefunded/g, 'Balance Refunded')
      .replace(/PaymentInformation/g, 'Payment Information')
      .replace(/AccountInformation/g, 'Account Information')
      .replace(/NetworkTimeout/g, 'Network Timeout')
      .replace(/SystemMaintenance/g, 'System Maintenance')
      .replace(/ConfirmDelivery/g, 'Confirm Delivery')
      .replace(/AutoConfirm/g, 'Auto Confirm')
      .replace(/IndependentSite/g, 'Independent Site')
      // 清理多余的空格
      .replace(/\s+/g, ' ')
      .trim();

    return translatedText;
  };

  // 翻译交易描述
  const translateTransactionDescription = (
    description: string,
    type: string,
  ) => {
    if (!description) return '';

    // 组件未挂载时返回原始描述
    if (!mounted) {
      return description;
    }

    // 在中文环境下，直接返回原始描述（确保显示完整信息）
    if (locale === 'zh') {
      return description;
    }

    // 只在英文环境下进行翻译
    try {
      // 1. 充值相关
      if (description.includes('钱包充值 - ')) {
        const match = description.match(
          /钱包充值 - (.+?)(?:\s*（含手续费([0-9.]+)）)?$/,
        );
        if (match) {
          const amount = match[1];
          const fee = match[2];

          if (fee) {
            return `Wallet Deposit - ${amount} (Including Fee $${fee})`;
          } else {
            return `Wallet Deposit - ${amount}`;
          }
        }
      }

      // 充值相关（标准格式）
      if (description.includes('充值 - ')) {
        const match = description.match(/充值 - (.+?)（手续费([0-9.]+)）$/);
        if (match) {
          const method = match[1];
          const fee = match[2];

          let translatedMethod = method;
          if (method === 'Alipay') {
            translatedMethod = 'Alipay';
          } else if (method === 'WeChat Pay') {
            translatedMethod = 'WeChat Pay';
          } else if (method === 'PayPal') {
            translatedMethod = 'PayPal';
          } else if (method === 'NOWPayments (Cryptocurrency)') {
            translatedMethod = 'NOWPayments (Cryptocurrency)';
          }

          return `Deposit - ${translatedMethod} (Fee $${fee})`;
        } else {
          const simpleMatch = description.match(/充值 - (.+)$/);
          if (simpleMatch) {
            const method = simpleMatch[1];

            let translatedMethod = method;
            if (method === 'Alipay') {
              translatedMethod = 'Alipay';
            } else if (method === 'WeChat Pay') {
              translatedMethod = 'WeChat Pay';
            } else if (method === 'PayPal') {
              translatedMethod = 'PayPal';
            } else if (method === 'NOWPayments (Cryptocurrency)') {
              translatedMethod = 'NOWPayments (Cryptocurrency)';
            }

            return `Deposit - ${translatedMethod}`;
          }
        }
      }

      // 2. 提现相关
      if (description.includes('提现申请 - ')) {
        const match = description.match(/提现申请 - (.+?)（手续费([0-9.]+)）$/);
        if (match) {
          const method = match[1];
          const fee = match[2];

          let translatedMethod = method;
          if (method === '美元账户') {
            translatedMethod = 'USD Account';
          } else if (method === 'USDT ERC20') {
            translatedMethod = 'USDT ERC20';
          } else if (method === 'USDT TRC20') {
            translatedMethod = 'USDT TRC20';
          }

          return `Withdrawal Application - ${translatedMethod} (Fee $${fee})`;
        } else {
          const simpleMatch = description.match(/提现申请 - (.+)$/);
          if (simpleMatch) {
            const method = simpleMatch[1];

            let translatedMethod = method;
            if (method === '美元账户') {
              translatedMethod = 'USD Account';
            } else if (method === 'USDT ERC20') {
              translatedMethod = 'USDT ERC20';
            } else if (method === 'USDT TRC20') {
              translatedMethod = 'USDT TRC20';
            }

            return `Withdrawal Application - ${translatedMethod}`;
          }
        }
      }

      // 提现申请拒绝退款
      if (description.includes('提现申请被拒绝，余额已返还')) {
        const match = description.match(/提现申请被拒绝，余额已返还 - (.+)$/);
        if (match) {
          const reason = match[1];
          return `Withdrawal Rejected, Balance Refunded - ${reason}`;
        } else {
          return 'Withdrawal Rejected, Balance Refunded';
        }
      }

      // 提现申请拒绝退款（简化格式）
      if (description.includes('提现申请拒绝退款')) {
        const match = description.match(/提现申请拒绝退款 - (.+)$/);
        if (match) {
          const reason = match[1];
          // 处理"未提供原因"的情况
          if (reason === '未提供原因') {
            return 'Withdrawal Request Rejected Refund - No reason provided';
          }
          return `Withdrawal Request Rejected Refund - ${reason}`;
        } else {
          return 'Withdrawal Request Rejected Refund';
        }
      }

      // 提现失败相关
      if (description.includes('提现失败')) {
        const match = description.match(/提现失败 - (.+)$/);
        if (match) {
          const reason = match[1];
          // 处理常见的失败原因
          if (reason === '收款信息错误') {
            return 'Withdrawal Failed - Incorrect payment information';
          } else if (reason === '银行信息错误') {
            return 'Withdrawal Failed - Incorrect bank information';
          } else if (reason === '网络超时') {
            return 'Withdrawal Failed - Network timeout';
          }
          return `Withdrawal Failed - ${reason}`;
        } else {
          return 'Withdrawal Failed';
        }
      }

      // 3. 委托相关费用
      // 发布委托费用
      if (description.includes('发布委托费用')) {
        if (description.includes('发布委托费用 - ')) {
          const title = description.replace('发布委托费用 - ', '');
          return `Task Publishing Fee - ${title}`;
        } else {
          return 'Task Publishing Fee';
        }
      }

      // 发布委托证据费（冻结中）
      if (description.includes('发布委托证据费（冻结中）')) {
        const match = description.match(/发布委托证据费（冻结中）- (.+)$/);
        if (match) {
          const title = match[1];
          return `Task Evidence Fee (Frozen) - ${title}`;
        } else {
          return 'Task Evidence Fee (Frozen)';
        }
      }

      // 接受委托押金
      if (description.includes('接受委托押金')) {
        const match = description.match(/接受委托押金 - (.+)$/);
        if (match) {
          const taskId = match[1];
          return `Task Acceptance Deposit - ${taskId}`;
        } else if (description.includes('接受委托押金冻结')) {
          const match2 = description.match(/接受委托押金冻结 - (.+)$/);
          if (match2) {
            const title = match2[1];
            return `Task Acceptance Deposit Freeze - ${title}`;
          }
        }
        return 'Task Acceptance Deposit';
      }

      // 委托押金相关（通用处理）
      if (description.includes('委托押金')) {
        // 委托押金退还
        if (description.includes('委托押金退还')) {
          const match = description.match(/委托押金退还 - (.+)$/);
          if (match) {
            const taskId = match[1];
            return `Task Deposit Refund - ${taskId}`;
          } else {
            return 'Task Deposit Refund';
          }
        }
        // 委托押金
        else {
          const match = description.match(/委托押金 - (.+)$/);
          if (match) {
            const taskId = match[1];
            return `Task Deposit - ${taskId}`;
          } else {
            return 'Task Deposit';
          }
        }
      }

      // 委托完成相关
      if (description.includes('委托完成')) {
        // 委托完成，证据费转为接单者酬金
        if (description.includes('委托完成，证据费转为接单者酬金')) {
          const match = description.match(
            /委托完成，证据费转为接单者酬金 - (.+)$/,
          );
          if (match) {
            const taskId = match[1];
            return `Task Completed, Evidence Fee Converted to Commission - ${taskId}`;
          }
        }
        // 委托完成押金释放
        else if (description.includes('委托完成押金释放')) {
          const match = description.match(/委托完成押金释放 - (.+)$/);
          if (match) {
            const taskId = match[1];
            return `Task Completed, Deposit Released - ${taskId}`;
          }
        }
        // 委托完成酬金
        else if (description.includes('委托完成酬金')) {
          const match = description.match(/委托完成酬金 - (.+)$/);
          if (match) {
            const taskId = match[1];
            return `Task Completion Commission - ${taskId}`;
          }
        }
      }

      // 完成委托获得酬金
      if (description.includes('完成委托获得酬金')) {
        if (description.includes('完成委托获得酬金 - ')) {
          const title = description.replace('完成委托获得酬金 - ', '');
          return `Commission from Completed Task - ${title}`;
        } else {
          return 'Commission from Completed Task';
        }
      }

      // 委托过期相关
      if (description.includes('委托过期')) {
        // 委托过期退款（发布费用）
        if (description.includes('委托过期退款（发布费用）')) {
          const match = description.match(/委托过期退款（发布费用）- (.+)$/);
          if (match) {
            const taskId = match[1];
            return `Task Expired Refund (Publishing Fee) - ${taskId}`;
          }
        }
        // 委托过期退款（证据费）
        else if (description.includes('委托过期退款（证据费）')) {
          const match = description.match(/委托过期退款（证据费）- (.+)$/);
          if (match) {
            const taskId = match[1];
            return `Task Expired Refund (Evidence Fee) - ${taskId}`;
          }
        }
        // 委托过期押金扣除
        else if (description.includes('委托过期押金扣除')) {
          const match = description.match(/委托过期押金扣除 - (.+)$/);
          if (match) {
            const taskId = match[1];
            return `Task Expired Deposit Deduction - ${taskId}`;
          }
        }
        // 委托过期退款（简单格式）
        else if (description.includes('委托过期退款')) {
          const match = description.match(/委托过期退款 - (.+)$/);
          if (match) {
            const taskId = match[1];
            return `Task Expired Refund - ${taskId}`;
          }
        }
      }

      // 委托取消相关
      if (description.includes('委托') && description.includes('取消退款')) {
        // 委托 {taskId} 取消退款（发布费用）
        if (description.includes('取消退款（发布费用）')) {
          const match = description.match(/委托 (.+?) 取消退款（发布费用）$/);
          if (match) {
            const taskId = match[1];
            return `Task ${taskId} Cancellation Refund (Publishing Fee)`;
          }
        }
        // 委托 {taskId} 取消退款（证据费）
        else if (description.includes('取消退款（证据费）')) {
          const match = description.match(/委托 (.+?) 取消退款（证据费）$/);
          if (match) {
            const taskId = match[1];
            return `Task ${taskId} Cancellation Refund (Evidence Fee)`;
          }
        }
      }

      // 4. 会员相关
      if (description.includes('会员')) {
        // 会员升级/续费 - Premium（含手续费2.00）
        if (
          description.includes('会员升级') ||
          description.includes('会员续费')
        ) {
          const upgradeMatch = description.match(
            /会员升级 - (.+?)（含手续费([0-9.]+)）$/,
          );
          const renewMatch = description.match(
            /会员续费 - (.+?)（含手续费([0-9.]+)）$/,
          );

          if (upgradeMatch) {
            const plan = upgradeMatch[1];
            const fee = upgradeMatch[2];
            return `Membership Upgrade - ${plan} (Including Fee $${fee})`;
          } else if (renewMatch) {
            const plan = renewMatch[1];
            const fee = renewMatch[2];
            return `Membership Renewal - ${plan} (Including Fee $${fee})`;
          }

          // 不含手续费的格式
          const simpleUpgradeMatch = description.match(/会员升级 - (.+)$/);
          const simpleRenewMatch = description.match(/会员续费 - (.+)$/);

          if (simpleUpgradeMatch) {
            const plan = simpleUpgradeMatch[1];
            return `Membership Upgrade - ${plan}`;
          } else if (simpleRenewMatch) {
            const plan = simpleRenewMatch[1];
            return `Membership Renewal - ${plan}`;
          }
        }

        // 升级会员套餐 - Premium
        if (description.includes('升级会员套餐')) {
          const match = description.match(/升级会员套餐 - (.+)$/);
          if (match) {
            const plan = match[1];
            return `Membership Plan Upgrade - ${plan}`;
          }
        }
      }

      // 5. 退款相关
      if (description.includes('退款')) {
        // 退款到钱包
        if (description.includes('退款到钱包')) {
          const match = description.match(/退款到钱包 - (.+)$/);
          if (match) {
            const reason = match[1];
            return `Refund to Wallet - ${reason}`;
          } else {
            return 'Refund to Wallet';
          }
        }

        // 证据费退还
        if (description.includes('证据费退还')) {
          const match = description.match(/证据费退还，退还委托用 - (.+)$/);
          if (match) {
            const title = match[1];
            return `Evidence Fee Refund for Task - ${title}`;
          } else {
            return 'Evidence Fee Refund';
          }
        }
      }

      // 6. 违约金和奖励金
      if (description.includes('违约金')) {
        // 违约金扣除
        if (description.includes('违约金扣除')) {
          const match = description.match(/违约金扣除 - (.+)$/);
          if (match) {
            const reason = match[1];
            return `Penalty Deduction - ${reason}`;
          }
        }

        // 放弃委托违约金
        if (description.includes('放弃委托违约金')) {
          const match = description.match(/放弃委托违约金 - (.+)$/);
          if (match) {
            const taskId = match[1];
            return `Task Abandonment Penalty - ${taskId}`;
          }
        }
      }

      // 接单者违约补偿（单独处理，因为不包含"违约金"字样）
      if (description.includes('接单者违约补偿')) {
        const match = description.match(/接单者违约补偿 - (.+)$/);
        if (match) {
          const taskId = match[1];
          return `Accepter Violation Compensation - ${taskId}`;
        }
      }

      if (description.includes('奖励金')) {
        const match = description.match(/奖励金 - (.+)$/);
        if (match) {
          const reason = match[1];
          return `Bonus Reward - ${reason}`;
        } else {
          return 'Bonus Reward';
        }
      }

      // 7. 委托审核相关
      if (description.includes('审核拒绝退款')) {
        // 委托 {taskId} 审核拒绝退款（发布费用）
        if (description.includes('审核拒绝退款（发布费用）')) {
          const match = description.match(
            /委托 (.+?) 审核拒绝退款（发布费用）$/,
          );
          if (match) {
            const taskId = match[1];
            return `Task ${taskId} Review Rejected Refund (Publishing Fee)`;
          }
        }
        // 委托 {taskId} 审核拒绝退款（证据费）
        else if (description.includes('审核拒绝退款（证据费）')) {
          const match = description.match(/委托 (.+?) 审核拒绝退款（证据费）$/);
          if (match) {
            const taskId = match[1];
            return `Task ${taskId} Review Rejected Refund (Evidence Fee)`;
          }
        }
      }

      // 8. 证据审核相关
      if (description.includes('证据审核通过')) {
        const match = description.match(
          /证据审核通过，退还证据费用 - 委托 (.+)$/,
        );
        if (match) {
          const taskId = match[1];
          return `Evidence Review Approved, Evidence Fee Refunded - Task ${taskId}`;
        } else {
          return 'Evidence Review Approved, Evidence Fee Refunded';
        }
      }

      // 9. 自动确认收货相关
      if (description.includes('自动确认收货')) {
        // 委托自动确认收货，证据费转为接单者酬金
        if (description.includes('证据费转为接单者酬金')) {
          const match = description.match(
            /委托自动确认收货，证据费转为接单者酬金 - (.+)$/,
          );
          if (match) {
            const taskId = match[1];
            return `Task Auto-Confirmed Delivery, Evidence Fee Converted to Commission - ${taskId}`;
          }
        }
      }

      // 10. 押金释放相关
      if (description.includes('押金释放')) {
        // 委托确认收货押金释放
        if (description.includes('确认收货押金释放')) {
          const match = description.match(/委托确认收货押金释放 - (.+)$/);
          if (match) {
            const taskId = match[1];
            return `Task Delivery Confirmed, Deposit Released - ${taskId}`;
          }
        }
        // 委托超时押金释放
        else if (description.includes('超时押金释放')) {
          const match = description.match(/委托超时押金释放 - (.+)$/);
          if (match) {
            const taskId = match[1];
            return `Task Timeout, Deposit Released - ${taskId}`;
          }
        }
      }

      // 11. 酬金支付相关
      if (description.includes('酬金支付')) {
        const match = description.match(/委托确认收货酬金支付 - (.+)$/);
        if (match) {
          const taskId = match[1];
          return `Task Delivery Confirmed, Commission Paid - ${taskId}`;
        }
      }

      // 如果没有找到特定模式，进行通用词汇翻译
      return translateCommonTerms(description);
    } catch (error) {
      console.warn('Translation error for transaction description:', error);
      return description;
    }
  };

  // 处理充值
  const handleDeposit = () => {
    const amount = parseFloat(depositAmount);
    if (!amount || amount <= 0) {
      toast.error(t('messages.invalidAmount'), {
        description: t('messages.invalidAmountDesc'),
      });
      return;
    }

    if (!depositMethod) {
      toast.error(t('messages.selectPaymentMethod'));
      return;
    }

    // 从支付方式配置中获取最小支付金额
    const selectedMethod = paymentMethods?.methods?.find(
      m => m.id === depositMethod,
    );
    const minAmount = selectedMethod?.minAmount || 1;

    if (amount < minAmount) {
      toast.error(t('messages.amountTooSmall'), {
        description: `${t('messages.amountTooSmallDesc')}${minAmount.toFixed(2)}`,
      });
      return;
    }

    depositMutation.mutate({
      amount,
      method: depositMethod,
    });

    setDepositAmount('');
  };

  // 修改提现处理函数
  const handleWithdraw = () => {
    if (!withdrawAmount || parseFloat(withdrawAmount) <= 0) {
      toast.error(t('messages.invalidWithdrawAmount'));
      return;
    }

    const amount = parseFloat(withdrawAmount);

    if (amount > (wallet?.balance || 0)) {
      toast.error(t('messages.insufficientBalance'));
      return;
    }

    if (
      systemRate?.minimumWithdrawalAmount &&
      amount < systemRate.minimumWithdrawalAmount
    ) {
      toast.error(
        `${t('messages.belowMinWithdraw')}${systemRate.minimumWithdrawalAmount.toFixed(2)}`,
      );
      return;
    }

    // 始终要求邮箱验证
    setPendingWithdrawalData({
      amount: withdrawAmount,
      method: withdrawMethod,
    });
    setIsVerificationDialogOpen(true);
  };

  // 新增：执行提现操作的函数
  // 修改 proceedWithWithdrawal 函数
  const proceedWithWithdrawal = (amount: string, method: string) => {
    // 根据提现方式打开相应的模态框
    if (method === 'BANK_CARD') {
      setIsBankWithdrawModalOpen(true);
    } else if (method === 'USDT') {
      // 确保传递正确的金额
      setWithdrawAmount(amount); // 保持金额状态
      setIsCryptoWithdrawModalOpen(true);
    }

    // 清除待处理数据，但不清空金额
    setPendingWithdrawalData(null);
  };

  // 同时修改 handleVerificationSuccess 函数
  const handleVerificationSuccess = (withdrawalToken: string) => {
    setIsVerificationDialogOpen(false);
    // setIsEmailVerified(true) // 不再需要全局状态
    setVerificationToken(withdrawalToken);

    toast.success(t('messages.verificationSuccess'), {
      description: t('messages.verificationSuccessDesc'),
    });

    if (!pendingWithdrawalData) return;

    // 执行提现操作，传递正确的金额
    proceedWithWithdrawal(
      pendingWithdrawalData.amount,
      pendingWithdrawalData.method,
    );
  };

  // 重置验证状态（可选：在用户登出或页面刷新时调用）
  const resetVerificationStatus = () => {
    // setIsEmailVerified(false) // 不再需要
    setVerificationToken('');
  };

  return (
    <div className='space-y-6'>
      {/* 页面标题 */}
      <div className='flex items-center justify-between'>
        <div>
          <h1 className='text-2xl font-bold tracking-tight'>
            {t('navigation.title')}
          </h1>
          <p className='text-muted-foreground'>{t('navigation.description')}</p>
        </div>
      </div>

      {/* 钱包概览卡片 */}
      <div className='grid grid-cols-1 md:grid-cols-4 gap-4'>
        <Card>
          <CardHeader className='flex flex-row items-center justify-between space-y-0 pb-2'>
            <CardTitle className='text-sm font-medium'>
              {t('balance.available')}
            </CardTitle>
            <Wallet className='h-4 w-4 text-muted-foreground' />
          </CardHeader>
          <CardContent>
            <div className='text-2xl font-bold text-green-600'>
              ${wallet?.balance?.toFixed(2) || '0.00'}
            </div>
            <p className='text-xs text-muted-foreground'>
              {t('balance.availableDescription')}
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className='flex flex-row items-center justify-between space-y-0 pb-2'>
            <CardTitle className='text-sm font-medium'>
              {t('balance.frozen')}
            </CardTitle>
            <AlertCircle className='h-4 w-4 text-muted-foreground' />
          </CardHeader>
          <CardContent>
            <div className='text-2xl font-bold text-orange-600'>
              ${wallet?.frozenAmount?.toFixed(2) || '0.00'}
            </div>
            <p className='text-xs text-muted-foreground'>
              {t('balance.frozenDescription')}
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className='flex flex-row items-center justify-between space-y-0 pb-2'>
            <CardTitle className='text-sm font-medium'>
              {t('balance.totalIncome')}
            </CardTitle>
            <TrendingUp className='h-4 w-4 text-muted-foreground' />
          </CardHeader>
          <CardContent>
            <div className='text-2xl font-bold text-blue-600'>
              ${wallet?.totalIncome?.toFixed(2) || '0.00'}
            </div>
            <p className='text-xs text-muted-foreground'>
              {t('balance.incomeDescription')}
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className='flex flex-row items-center justify-between space-y-0 pb-2'>
            <CardTitle className='text-sm font-medium'>
              {t('balance.totalExpense')}
            </CardTitle>
            <TrendingDown className='h-4 w-4 text-muted-foreground' />
          </CardHeader>
          <CardContent>
            <div className='text-2xl font-bold text-purple-600'>
              ${wallet?.totalExpense?.toFixed(2) || '0.00'}
            </div>
            <p className='text-xs text-muted-foreground'>
              {t('balance.expenseDescription')}
            </p>
          </CardContent>
        </Card>
      </div>

      {/* 充值提现区域 */}
      <div className='grid grid-cols-1 md:grid-cols-2 gap-4'>
        {/* 充值卡片 */}
        <Card>
          <CardHeader>
            <CardTitle className='flex items-center gap-2'>
              <Plus className='h-5 w-5 text-green-600' />
              {t('deposit.title')}
            </CardTitle>
          </CardHeader>
          <CardContent className='space-y-4'>
            <div className='space-y-2'>
              <Label htmlFor='deposit-amount'>{t('deposit.amount')}</Label>
              <Input
                id='deposit-amount'
                type='number'
                placeholder={t('deposit.amountPlaceholder')}
                value={depositAmount}
                onChange={e => setDepositAmount(e.target.value)}
              />
              <p className='text-xs text-muted-foreground'>
                {t('deposit.minAmount')}: $
                {paymentMethods?.methods
                  ?.find(m => m.id === depositMethod)
                  ?.minAmount?.toFixed(2) || '1.00'}
              </p>
            </div>

            <div className='space-y-2'>
              <Label htmlFor='deposit-method'>
                {t('deposit.paymentMethod')}
              </Label>
              <div className='grid grid-cols-2 gap-4'>
                {/* NOWPayments */}
                {paymentMethods?.methods?.find(m =>
                  m.name.includes('NOWPayments'),
                ) && (
                  <div
                    key={'nowpayments'}
                    className={`border rounded-md p-4 flex flex-col items-center justify-center space-y-2 cursor-pointer ${
                      depositMethod ===
                      paymentMethods?.methods?.find(m =>
                        m.name.includes('NOWPayments'),
                      )?.id
                        ? 'border-primary ring-2 ring-primary'
                        : 'border-gray-200 dark:border-gray-700'
                    }`}
                    onClick={() =>
                      setDepositMethod(
                        paymentMethods?.methods?.find(m =>
                          m.name.includes('NOWPayments'),
                        )?.id || '',
                      )
                    }
                  >
                    <Bitcoin className='h-8 w-8' />
                    <span className='text-sm font-medium'>
                      {
                        paymentMethods?.methods?.find(m =>
                          m.name.includes('NOWPayments'),
                        )?.name
                      }
                    </span>
                    <Badge variant='secondary' className='text-xs'>
                      {
                        paymentMethods?.methods?.find(m =>
                          m.name.includes('NOWPayments'),
                        )?.feeRate
                      }
                      %
                    </Badge>
                  </div>
                )}

                {/* 其他支付方式 */}
                {paymentMethods?.methods
                  ?.filter(m => !m.name.includes('NOWPayments'))
                  .map(method => (
                    <div
                      key={method.id}
                      className={`border rounded-md p-4 flex flex-col items-center justify-center space-y-2 cursor-pointer ${
                        depositMethod === method.id
                          ? 'border-primary ring-2 ring-primary'
                          : 'border-gray-200 dark:border-gray-700'
                      }`}
                      onClick={() => setDepositMethod(method.id)}
                    >
                      {getPaymentIcon(method.id)}
                      <span className='text-sm font-medium'>{method.name}</span>
                      <Badge variant='secondary' className='text-xs'>
                        {method.feeRate}%
                      </Badge>
                    </div>
                  ))}
              </div>
            </div>

            <Button
              onClick={handleDeposit}
              className='w-full'
              disabled={depositMutation.isPending}
            >
              <Plus className='h-4 w-4 mr-2' />
              {depositMutation.isPending
                ? t('deposit.processing')
                : t('deposit.submit')}
            </Button>
          </CardContent>
        </Card>

        {/* 提现卡片 */}
        <Card>
          <CardHeader>
            <CardTitle className='flex items-center gap-2'>
              <Minus className='h-5 w-5 text-red-600' />
              {t('withdraw.title')}
            </CardTitle>
          </CardHeader>
          <CardContent className='space-y-4'>
            <div className='space-y-2'>
              <Label htmlFor='withdraw-amount'>{t('withdraw.amount')}</Label>
              <Input
                id='withdraw-amount'
                type='number'
                placeholder={t('withdraw.amountPlaceholder')}
                value={withdrawAmount}
                onChange={e => setWithdrawAmount(e.target.value)}
              />
              <p className='text-xs text-muted-foreground'>
                {systemRate?.minimumWithdrawalAmount !== undefined
                  ? `${t('withdraw.minAmount')}: $${systemRate.minimumWithdrawalAmount.toFixed(2)}, `
                  : ''}
                {t('withdraw.availableBalance')}: $
                {wallet?.balance?.toFixed(2) || '0.00'}
              </p>
            </div>

            <div className='space-y-2'>
              <Label htmlFor='withdraw-method'>{t('withdraw.method')}</Label>
              <div className='grid grid-cols-2 gap-4'>
                <div
                  className={`border rounded-md p-4 flex flex-col items-center justify-center space-y-2 cursor-pointer ${
                    withdrawMethod === 'BANK_CARD'
                      ? 'border-primary ring-2 ring-primary'
                      : 'border-gray-200 dark:border-gray-700'
                  }`}
                  onClick={() => setWithdrawMethod('BANK_CARD')}
                >
                  <CreditCard className='h-8 w-8' />
                  <span className='text-sm font-medium'>
                    {t('withdraw.bankAccount')}
                  </span>
                  {systemRate?.bankWithdrawalRate !== undefined && (
                    <Badge variant='secondary' className='text-xs'>
                      {systemRate.bankWithdrawalRate}%
                    </Badge>
                  )}
                </div>
                <div
                  className={`border rounded-md p-4 flex flex-col items-center justify-center space-y-2 cursor-pointer ${
                    withdrawMethod === 'USDT'
                      ? 'border-primary ring-2 ring-primary'
                      : 'border-gray-200 dark:border-gray-700'
                  }`}
                  onClick={() => setWithdrawMethod('USDT')}
                >
                  <Bitcoin className='h-8 w-8' />
                  <span className='text-sm font-medium'>USDT</span>
                  {systemRate?.erc20WithdrawalRate !== undefined &&
                    systemRate?.trc20WithdrawalRate !== undefined && (
                      <div className='flex flex-col gap-1 items-center'>
                        <Badge variant='secondary' className='text-xs'>
                          ERC20: {systemRate.erc20WithdrawalRate}%
                        </Badge>
                        <Badge variant='secondary' className='text-xs'>
                          TRC20: {systemRate.trc20WithdrawalRate}%
                        </Badge>
                      </div>
                    )}
                </div>
              </div>
            </div>

            <Button
              onClick={handleWithdraw}
              variant='outline'
              className='w-full'
              disabled={withdrawMutation.isPending}
            >
              {!isEmailVerified && <Shield className='h-4 w-4 mr-2' />}
              <Minus className='h-4 w-4 mr-2' />
              {withdrawMutation.isPending
                ? t('withdraw.processing')
                : isEmailVerified
                  ? t('withdraw.submit')
                  : t('withdraw.verifyAndSubmit')}
            </Button>
          </CardContent>
        </Card>
      </div>

      {/* 交易记录 */}
      <Card>
        <CardHeader>
          <CardTitle>{t('transactions.title')}</CardTitle>
        </CardHeader>
        <CardContent>
          <ResponsiveTransactionTabs
            activeTab={activeTab}
            onTabChange={setActiveTab}
            t={t}
          >
            <TabsContent value={activeTab} className='mt-4'>
              <TransactionList
                transactions={filteredTransactions}
                t={t}
                tTransactions={tTransactions}
                formatDateTime={formatDateTime}
                translateTransactionDescription={translateTransactionDescription}
                getTypeIcon={getTypeIcon}
                getStatusColor={getStatusColor}
                getStatusIcon={getStatusIcon}
              />
            </TabsContent>
          </ResponsiveTransactionTabs>
        </CardContent>
      </Card>

      {/* 邮箱验证对话框 */}
      <WithdrawalVerificationDialog
        open={isVerificationDialogOpen}
        onOpenChange={open => {
          if (!open) {
            setPendingWithdrawalData(null);
          }
          setIsVerificationDialogOpen(open);
        }}
        onVerificationSuccess={handleVerificationSuccess}
      />

      {/* 提现模态框 */}
      <BankWithdrawalModal
        isOpen={isBankWithdrawModalOpen}
        onClose={() => setIsBankWithdrawModalOpen(false)}
        initialAmount={withdrawAmount}
        userBalance={wallet?.balance || 0}
        withdrawalToken={verificationToken} // 传递验证token
      />

      <CryptoWithdrawalModal
        isOpen={isCryptoWithdrawModalOpen}
        onClose={() => setIsCryptoWithdrawModalOpen(false)}
        initialAmount={withdrawAmount}
        userBalance={wallet?.balance || 0}
        selectedNetwork='USDT_ERC20'
        withdrawalToken={verificationToken} // 传递验证token
      />
    </div>
  );
}
