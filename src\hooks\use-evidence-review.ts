import { useMutation, useQueryClient } from '@tanstack/react-query';

interface EvidenceReviewRequest {
  taskId: string;
  action: 'approve' | 'reject';
  reason?: string;
}

interface EvidenceReviewResponse {
  success: boolean;
  message: string;
  data: {
    taskId: string;
    evidenceStatus: string;
    action: string;
    reason?: string;
  };
}

// 证据审核Hook
export function useEvidenceReview() {
  const queryClient = useQueryClient();

  return useMutation<EvidenceReviewResponse, Error, EvidenceReviewRequest>({
    mutationFn: async ({ taskId, action, reason }) => {
      const response = await fetch('/api/admin/evidence/review', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          taskId,
          action,
          reason,
        }),
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.error || '审核失败');
      }

      return response.json();
    },
    onSuccess: () => {
      // 审核成功后刷新证据列表和委托列表
      queryClient.invalidateQueries({
        queryKey: ['evidence-data'],
        exact: false,
      });
      queryClient.invalidateQueries({
        queryKey: ['admin-all-tasks'],
        exact: false,
      });
    },
  });
}

export type { EvidenceReviewRequest, EvidenceReviewResponse };
