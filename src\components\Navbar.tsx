'use client';

import { ArrowR<PERSON> } from 'lucide-react';
import Link from 'next/link';
import { usePathname } from 'next/navigation';
import { useSession } from 'next-auth/react';

import { cn } from '@/lib/utils';
import { useTheme } from 'next-themes';

import MaxWidthWrapper from './MaxWidthWrapper';
import { ModeToggle } from './mode-toggle';
import { RefundGoLogoCompact } from './refund-go-logo';
import { buttonVariants } from './ui/button';
import UserAccountNav from './UserAccountNav';

function Navbar() {
  const { data: session } = useSession();
  const { theme } = useTheme();
  const pathname = usePathname();

  // 定义使用sidebar的页面路径
  const sidebarPages = [
    '/dashboard',
    '/profile',
    '/tasks',
    '/publish',
    '/my-accepted-tasks',
    '/my-published-tasks',
    '/wallet',
    '/membership',
    '/tickets',
    '/whitelist',
  ];
  const isOnSidebarPage = sidebarPages.some(page => pathname.startsWith(page));

  // 如果在sidebar页面，不显示导航栏
  if (isOnSidebarPage) {
    return null;
  }

  return (
    <nav className='fixed inset-x-0 top-0 z-50 w-full bg-white/80 backdrop-blur-md border-b border-gray-200/50 transition-all dark:bg-gray-950/80 dark:border-gray-800/50'>
      <MaxWidthWrapper>
        <div className='flex h-16 items-center justify-between'>
          <Link href='/' className='flex z-40'>
            <RefundGoLogoCompact animated={true} darkMode={theme === 'dark'} />
          </Link>

          <div className='hidden md:flex items-center justify-center gap-14'>
            <Link
              href='#pricing'
              className='hover:underline hover:underline-offset-2 text-gray-700 hover:text-gray-900 transition-colors dark:text-gray-300 dark:hover:text-gray-100'
            >
              价格
            </Link>
            <Link
              href='#demo'
              className='hover:underline hover:underline-offset-2 text-gray-700 hover:text-gray-900 transition-colors dark:text-gray-300 dark:hover:text-gray-100'
            >
              演示
            </Link>
            <Link
              href='#faq'
              className='hover:underline hover:underline-offset-2 text-gray-700 hover:text-gray-900 transition-colors dark:text-gray-300 dark:hover:text-gray-100'
            >
              常见问题
            </Link>
          </div>

          <div className='flex items-center space-x-1.5'>
            <ModeToggle />
            <>
              {session?.user ? (
                <div className='flex items-center space-x-4'>
                  <Link
                    href='/dashboard'
                    className={buttonVariants({
                      size: 'sm',
                      className:
                        'flex items-center justify-center text-xs group px-4 py-5',
                    })}
                  >
                    <span>仪表盘</span>
                    <ArrowRight className='ml-1.5 transform size-4 transition-transform duration-300 group-hover:translate-x-1' />
                  </Link>
                  <div className='h-8 w-px bg-gray-200 dark:bg-gray-700' />
                  <UserAccountNav session={session} />
                </div>
              ) : (
                <div className='flex items-center space-x-2'>
                  <Link
                    href='/sign-in'
                    className={cn(
                      buttonVariants({ variant: 'ghost', size: 'sm' }),
                      'flex items-center justify-center group px-4 py-5',
                    )}
                  >
                    <span>登录</span>
                  </Link>
                  <Link
                    href='/sign-up'
                    className={cn(
                      buttonVariants({ size: 'sm' }),
                      'flex items-center justify-center group px-4 py-5',
                    )}
                  >
                    <span>注册</span>
                    <ArrowRight className='ml-1.5 transform size-4 transition-transform duration-300 group-hover:translate-x-1' />
                  </Link>
                </div>
              )}
            </>
          </div>
        </div>
      </MaxWidthWrapper>
    </nav>
  );
}

export default Navbar;
