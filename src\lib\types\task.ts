export interface Task {
  id: string;
  title?: string;
  productUrl?: string;
  productDescription?: string;
  quantity: number;
  unitPrice: number;
  totalAmount: number;
  totalPrice?: number; // 兼容字段
  listingTime?: string;
  recipientName?: string;
  recipientPhone?: string;
  shippingAddress?: string;
  evidenceUploadType?: EvidenceUploadType;
  cartScreenshots?: string[];
  evidenceFiles?: string[];
  platformId?: string;
  categoryId?: string;
  chargebackTypeIds?: string[];
  paymentMethodIds?: string[];
  finalTotal?: number;

  // 实际使用的字段 - 可能是枚举值、字符串或对象
  platform: Platform | string | { id: string; name: string };
  category: string | { id: string; name: string };
  chargebackTypes?: ChargebackType[] | string[];
  paymentMethods?: PaymentMethod[] | string[];
  commission?: number; // 酬金字段，用于兼容现有代码
  deadline?: Date; // 兼容字段
  timeLimit?: number;
  deposit?: number;
  description?: string;

  status: TaskStatus;
  evidenceStatus?: EvidenceStatus | string;
  evidenceRejectReason?: string;
  rejectedAt?: Date;
  approvedAt?: Date;
  publishedAt?: Date;
  acceptedAt?: Date;
  expiresAt?: Date;
  completedAt?: Date;
  publisherId?: string;
  accepterId?: string;
  createdAt: Date;
  updatedAt: Date;

  // 物流信息
  trackingNumber?: string;
  logisticsScreenshots?: string[];

  // 审核信息
  reviewedAt?: Date;
  reviewRejectReason?: string;

  // 订单信息
  orderNumber?: string;
  orderScreenshot?: string;

  // 关联数据
  publisher?: {
    id: string;
    name: string | null;
    email: string | null;
  };
  accepter?: {
    id: string;
    name: string | null;
    email: string | null;
  };

  // 截止时间相关
  logisticsDeadline?: Date; // 物流号提交截止时间（提交订单后5天）
  orderReviewDeadline?: Date; // 订单审核截止时间（提交订单后5小时）
  logisticsReviewDeadline?: Date; // 物流审核截止时间（提交物流后24小时）
  deliveryDeadline?: Date; // 确认收货截止时间（审核通过后30天）
}

export enum Platform {
  AMAZON = 'amazon',
  EBAY = 'ebay',
  SHOPIFY = 'shopify',
  WALMART = 'walmart',
  ETSY = 'etsy',
  ALIEXPRESS = 'aliexpress',
}

export enum ChargebackType {
  FRAUD = 'fraud',
  AUTHORIZATION = 'authorization',
  PROCESSING_ERROR = 'processing_error',
  CONSUMER_DISPUTE = 'consumer_dispute',
  NON_RECEIPT = 'non_receipt',
  DUPLICATE_PROCESSING = 'duplicate_processing',
  CREDIT_NOT_PROCESSED = 'credit_not_processed',
}

export enum PaymentMethod {
  CREDIT_CARD = 'credit_card',
  DEBIT_CARD = 'debit_card',
  PAYPAL = 'paypal',
  APPLE_PAY = 'apple_pay',
  GOOGLE_PAY = 'google_pay',
  BANK_TRANSFER = 'bank_transfer',
  CRYPTOCURRENCY = 'cryptocurrency',
}

export enum TaskStatus {
  PENDING = 'PENDING', // 审核中
  REJECTED = 'REJECTED', // 未通过
  RECRUITING = 'RECRUITING', // 招募中
  IN_PROGRESS = 'IN_PROGRESS', // 进行中
  PENDING_LOGISTICS = 'PENDING_LOGISTICS', // 等待物流单号
  PENDING_REVIEW = 'PENDING_REVIEW', // 等待发布者审核
  PENDING_DELIVERY = 'PENDING_DELIVERY', // 等待确认收货
  COMPLETED = 'COMPLETED', // 已完成
  EXPIRED = 'EXPIRED', // 已过期
  CANCELLED = 'CANCELLED', // 已取消
}

export enum EvidenceStatus {
  PENDING_SUBMISSION = 'PENDING_SUBMISSION', // 待提交
  UNDER_REVIEW = 'UNDER_REVIEW', // 审核中
  NO_EVIDENCE = 'NO_EVIDENCE', // 无证据
  REVIEWED = 'REVIEWED', // 已审核
  REJECTED = 'REJECTED', // 已拒绝
}

export enum EvidenceUploadType {
  IMMEDIATE = 'IMMEDIATE', // 立即上传
  LATER = 'LATER', // 稍后上传
  NONE = 'NONE', // 无证据
}

export interface TaskFilters {
  search?: string;
  platform?: string[]; // 使用平台ID字符串数组
  chargebackType?: string[]; // 使用拒付类型ID字符串数组
  paymentMethod?: string[]; // 使用支付方式ID字符串数组
  status?: TaskStatus[];
  evidenceStatus?: EvidenceStatus[];
  sortBy?: 'deadline' | 'commission' | 'createdAt';
  sortOrder?: 'asc' | 'desc';
}

export const PLATFORM_LABELS: Record<Platform, string> = {
  [Platform.AMAZON]: 'Amazon',
  [Platform.EBAY]: 'eBay',
  [Platform.SHOPIFY]: 'Shopify',
  [Platform.WALMART]: 'Walmart',
  [Platform.ETSY]: 'Etsy',
  [Platform.ALIEXPRESS]: 'AliExpress',
};

export const CHARGEBACK_TYPE_LABELS: Record<ChargebackType, string> = {
  [ChargebackType.FRAUD]: '欺诈',
  [ChargebackType.AUTHORIZATION]: '授权问题',
  [ChargebackType.PROCESSING_ERROR]: '处理错误',
  [ChargebackType.CONSUMER_DISPUTE]: '消费者争议',
  [ChargebackType.NON_RECEIPT]: '未收到商品',
  [ChargebackType.DUPLICATE_PROCESSING]: '重复处理',
  [ChargebackType.CREDIT_NOT_PROCESSED]: '退款未处理',
};

export const PAYMENT_METHOD_LABELS: Record<PaymentMethod, string> = {
  [PaymentMethod.CREDIT_CARD]: '信用卡',
  [PaymentMethod.DEBIT_CARD]: '借记卡',
  [PaymentMethod.PAYPAL]: 'PayPal',
  [PaymentMethod.APPLE_PAY]: 'Apple Pay',
  [PaymentMethod.GOOGLE_PAY]: 'Google Pay',
  [PaymentMethod.BANK_TRANSFER]: '银行转账',
  [PaymentMethod.CRYPTOCURRENCY]: '加密货币',
};

export const TASK_STATUS_LABELS: Record<TaskStatus, string> = {
  [TaskStatus.PENDING]: '审核中',
  [TaskStatus.REJECTED]: '未通过',
  [TaskStatus.RECRUITING]: '招募中',
  [TaskStatus.IN_PROGRESS]: '进行中',
  [TaskStatus.PENDING_LOGISTICS]: '等待物流单号',
  [TaskStatus.PENDING_REVIEW]: '等待审核',
  [TaskStatus.PENDING_DELIVERY]: '等待收货',
  [TaskStatus.COMPLETED]: '已完成',
  [TaskStatus.EXPIRED]: '已过期',
  [TaskStatus.CANCELLED]: '已取消',
};

export const EVIDENCE_STATUS_LABELS: Record<EvidenceStatus, string> = {
  [EvidenceStatus.PENDING_SUBMISSION]: '待上传',
  [EvidenceStatus.UNDER_REVIEW]: '待审核',
  [EvidenceStatus.NO_EVIDENCE]: '无证据',
  [EvidenceStatus.REVIEWED]: '已审核',
  [EvidenceStatus.REJECTED]: '未通过',
};

export const TASK_STATUS_COLORS: Record<TaskStatus, string> = {
  [TaskStatus.PENDING]: 'text-yellow-600 bg-yellow-50 border-yellow-200',
  [TaskStatus.REJECTED]: 'text-red-600 bg-red-50 border-red-200',
  [TaskStatus.RECRUITING]: 'text-blue-600 bg-blue-50 border-blue-200',
  [TaskStatus.IN_PROGRESS]: 'text-orange-600 bg-orange-50 border-orange-200',
  [TaskStatus.PENDING_LOGISTICS]:
    'text-purple-600 bg-purple-50 border-purple-200',
  [TaskStatus.PENDING_REVIEW]: 'text-indigo-600 bg-indigo-50 border-indigo-200',
  [TaskStatus.PENDING_DELIVERY]: 'text-cyan-600 bg-cyan-50 border-cyan-200',
  [TaskStatus.COMPLETED]: 'text-green-600 bg-green-50 border-green-200',
  [TaskStatus.EXPIRED]: 'text-gray-600 bg-gray-50 border-gray-200',
  [TaskStatus.CANCELLED]: 'text-gray-600 bg-gray-50 border-gray-200',
};

export const EVIDENCE_STATUS_COLORS: Record<EvidenceStatus, string> = {
  [EvidenceStatus.PENDING_SUBMISSION]:
    'text-gray-600 bg-gray-50 border-gray-200',
  [EvidenceStatus.UNDER_REVIEW]:
    'text-yellow-600 bg-yellow-50 border-yellow-200',
  [EvidenceStatus.NO_EVIDENCE]:
    'text-orange-600 bg-orange-50 border-orange-200',
  [EvidenceStatus.REVIEWED]: 'text-green-600 bg-green-50 border-green-200',
  [EvidenceStatus.REJECTED]: 'text-red-600 bg-red-50 border-red-200',
};

export interface TaskListItem {
  id: string;
  title?: string;
  status: TaskStatus;
  finalTotal: number;
  platform: { name: string };
  category: { name: string };
  createdAt: Date;
  updatedAt: Date;
}

export interface TaskStats {
  total: number;
  pending: number;
  recruiting: number;
  inProgress: number;
  completed: number;
  rejected: number;
  expired: number;
  cancelled: number;
}
