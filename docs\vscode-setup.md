# VSCode Development Environment Setup Guide

This document details the VSCode development environment configuration for the project, based on
Context7 best practices and Next.js + TypeScript tech stack.

## 📁 Configuration File Structure

```
.vscode/
├── settings.json          # Workspace settings
├── extensions.json        # Recommended extensions
├── tasks.json            # Task configuration
├── launch.json           # Debug configuration
└── snippets.code-snippets # Code snippets
```

## ⚙️ Core Configuration Details

### 1. Editor Settings (settings.json)

#### Basic Editor Configuration

- **Indentation**: 2 spaces, disable auto-detection
- **Line wrapping**: 80/100 character rulers
- **Formatting**: Auto-format on save
- **Whitespace**: Auto-trim trailing spaces

#### Code Quality Tools

- **ESLint**: Enable Flat Config, real-time checking
- **Prettier**: Unified code formatting
- **TypeScript**: Intelligent suggestions and type checking

#### File Management

- **Auto Save**: 1 second delay
- **File Associations**:
  - `*.json` → `jsonc`
  - `*.mdx` → `markdown`
  - `.env*` → `dotenv`
  - `*.css` → `tailwindcss`

#### Terminal Settings

- **Windows**: Default to PowerShell
- **macOS**: Default to zsh, supports bash and fish
- **Working Directory**: Auto-set to workspace root

### 2. Recommended Extensions (extensions.json)

#### AI Development Assistant

- **Augment**: AI-powered coding assistant for enhanced development experience

#### Core Development Tools

- **ESLint**: Code quality checking
- **Prettier**: Code formatting
- **TypeScript**: Type support
- **Tailwind CSS**: Style intelligent suggestions

#### React/Next.js Development

- **ES7+ React/Redux/React-Native snippets**: Code snippets
- **Auto Rename Tag**: Auto-rename tags
- **Path Intellisense**: Path intelligent suggestions

#### Testing Tools

- **Jest**: Unit testing support
- **Playwright**: E2E testing support

#### Git Tools

- **GitLens**: Git enhanced features

#### Additional Recommended Extensions

- **Error Lens**: Real-time error display
- **Material Icon Theme**: File icon theme
- **Code Spell Checker**: Spell checking
- **DotENV**: Environment variable syntax highlighting

### 3. 委托配置 (tasks.json)

#### 开发委托

- **Start Development Server**: 启动开发服务器 (npm run dev)
- `npm: build` - 构建生产版本
- `npm: test` - 运行测试
- `npm: lint` - 代码检查
- `npm: format` - 代码格式化

#### 实用委托

- `TypeScript: Check` - 类型检查
- `Database: Seed` - 数据库种子数据 (npm run db:seed)
- `Database: Reset` - 重置数据库 (npm run db:reset)
- `Prisma: Studio` - 打开数据库管理界面

### 4. 调试配置 (launch.json)

#### Next.js 调试

- **Next.js: debug server-side**: 服务端调试
- **客户端调试**: 通过 Chrome DevTools
- **自动启动**: 开发服务器启动时自动打开调试器

#### 调试功能

- 断点调试支持
- 变量检查
- 调用堆栈查看
- 实时代码修改

### 5. 代码片段 (snippets.code-snippets)

#### React 组件

- `rfc` - React 函数组件
- `rfcp` - 带 Props 的 React 组件
- `rhook` - 自定义 Hook

#### Next.js

- `npage` - Next.js 页面组件
- `napi` - Next.js API 路由

#### 测试

- `jtest` - Jest 测试套件

#### TypeScript

- `tsi` - TypeScript interface
- `tst` - TypeScript type

## 🚀 Quick Start

### 1. Install Recommended Extensions

Open VSCode command palette (`Ctrl+Shift+P`) and run:

```
Extensions: Show Recommended Extensions
```

Click "Install All" to install all recommended extensions, including:

- **Augment**: AI-powered coding assistant (automatically included)
- All other development tools and utilities

### 2. Verify Configuration

1. Open any TypeScript file and confirm:
   - ESLint errors are displayed
   - Prettier formatting works
   - TypeScript type hints are working

2. Run development tasks:
   - `Ctrl+Shift+P` → `Tasks: Run Task` → `npm: dev`

3. Test debugging functionality:
   - `F5` to start debugging
   - Set breakpoints for testing

### 3. Custom Configuration

根据个人喜好调整 `settings.json` 中的：

- 主题和图标
- 字体和字号
- 快捷键绑定

## 📝 最佳实践

### 代码质量

1. **保存时自动修复**: ESLint 错误自动修复
2. **导入排序**: 自动组织和排序导入语句
3. **类型检查**: 实时 TypeScript 类型检查

### 开发效率

1. **代码片段**: 使用预定义片段快速生成代码
2. **智能提示**: 充分利用 IntelliSense
3. **快捷键**: 熟悉常用快捷键

### 团队协作

1. **统一配置**: 所有团队成员使用相同配置
2. **扩展同步**: 确保安装推荐扩展
3. **代码风格**: 遵循 ESLint 和 Prettier 规则

## 🔧 故障排除

### 常见问题

#### ESLint 不工作

1. 确认安装了 ESLint 扩展
2. 检查 `eslint.experimental.useFlatConfig` 设置
3. 重启 ESLint 服务器：`Ctrl+Shift+P` → `ESLint: Restart ESLint Server`

#### TypeScript 错误

1. 确认 TypeScript 版本兼容
2. 重新加载窗口：`Ctrl+Shift+P` → `Developer: Reload Window`
3. 检查 `tsconfig.json` 配置

#### Prettier 格式化问题

1. 确认 Prettier 扩展已启用
2. 检查文件类型关联
3. 验证 Prettier 配置文件

### 性能优化

#### 大型项目优化

1. 排除不必要的文件夹（已配置）
2. 限制文件监视器范围
3. 调整 TypeScript 服务器内存

#### 扩展管理

1. 禁用不需要的扩展
2. 定期更新扩展
3. 监控扩展性能影响

## 🌐 国际化开发支持

### i18n 文件管理

项目支持中英文双语，相关文件结构：

```
messages/
├── en/                    # 英文翻译模块
├── zh/                    # 中文翻译模块
├── en.json               # 英文主翻译文件
└── zh.json               # 中文主翻译文件
```

### 推荐工作流

1. **翻译文件编辑**: 使用 JSON 语法高亮和验证
2. **脚本执行**: 使用集成终端运行 i18n 脚本
3. **实时预览**: 开发服务器自动重载翻译更改

### 有用的命令

```bash
# 验证翻译完整性
node scripts/validate-translations.js

# 迁移翻译文件
node scripts/migrate-translations.js
```

## 📚 相关资源

- [Next.js 官方文档](https://nextjs.org/docs)
- [next-intl 文档](https://next-intl-docs.vercel.app/)
- [TypeScript 配置参考](https://www.typescriptlang.org/tsconfig)
- [ESLint 配置指南](https://eslint.org/docs/user-guide/configuring)
- [Prettier 配置选项](https://prettier.io/docs/en/configuration.html)
- [VSCode 设置参考](https://code.visualstudio.com/docs/getstarted/settings)

## 🤝 贡献

如需改进配置或添加新功能：

1. 创建功能分支
2. 更新相关配置文件
3. 测试配置有效性
4. 更新此文档
5. 提交 Pull Request

---

_此配置基于 Context7 最佳实践和现代 Web 开发标准制定，旨在提供最佳的开发体验。_
