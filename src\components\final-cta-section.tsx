"use client";

import { <PERSON>R<PERSON>, Users, TrendingUp, DollarSign, Clock } from "lucide-react";
import { useTranslations } from "next-intl";

import { Button } from "@/components/ui/button";

export function FinalCTASection() {
  const t = useTranslations("HomePage");
  const statistics = [
    {
      icon: Users,
      value: "20K+",
      label: t("cta.stats.activeUsers"),
      color: "text-blue-600",
    },
    {
      icon: TrendingUp,
      value: "98.5%",
      label: t("cta.stats.successRate"),
      color: "text-green-600",
    },
    {
      icon: DollarSign,
      value: "$13M+",
      label: t("cta.stats.transactionVolume"),
      color: "text-purple-600",
    },
    {
      icon: Clock,
      value: "24/7",
      label: t("cta.stats.customerSupport"),
      color: "text-orange-600",
    },
  ];

  return (
    <section className="py-20 bg-gradient-to-br from-gradient-primary-from to-gradient-primary-to text-white">
      <div className="container mx-auto px-4 text-center">
        {/* Main Content */}
        <div className="max-w-4xl mx-auto mb-16">
          <h2 className="text-4xl md:text-6xl font-bold mb-6 text-white">{t("cta.title")}</h2>
          <p className="text-xl md:text-2xl mb-12 text-white">{t("cta.subtitle")}</p>

          {/* Action Buttons */}
          <div className="flex flex-col sm:flex-row gap-4 justify-center items-center mb-16">
            <Button
              size="lg"
              className="bg-white text-gradient-primary-from hover:bg-gray-50 hover:text-gradient-primary-from/90 focus:bg-gray-50 focus:text-gradient-primary-from/90 focus:ring-4 focus:ring-white/30 focus:outline-none px-8 py-4 text-lg font-semibold shadow-lg transition-all duration-300 min-h-[48px]"
              aria-label="Get started with RefundGo platform"
            >
              {t("cta.getStarted")}
              <ArrowRight className="ml-2 h-5 w-5" />
            </Button>
            <Button
              variant="outline"
              size="lg"
              className="border-2 border-white text-white hover:bg-white hover:text-gradient-primary-from focus:bg-white focus:text-gradient-primary-from focus:ring-4 focus:ring-white/30 focus:outline-none px-8 py-4 text-lg font-semibold bg-transparent transition-all duration-300 min-h-[48px]"
              aria-label="Browse available tasks"
            >
              {t("cta.browseTasks")}
            </Button>
          </div>
        </div>

        {/* Statistics Grid */}
        <div className="grid grid-cols-2 md:grid-cols-4 gap-8 max-w-4xl mx-auto">
          {statistics.map((stat, index) => (
            <div key={index} className="text-center">
              <div className="inline-flex items-center justify-center w-16 h-16 bg-white/30 rounded-full mb-4 backdrop-blur-sm border border-white/20">
                <stat.icon className="w-8 h-8 text-white" />
              </div>
              <div className="text-3xl font-bold mb-2 text-white">{stat.value}</div>
              <div className="text-white text-sm font-medium">{stat.label}</div>
            </div>
          ))}
        </div>
      </div>
    </section>
  );
}
