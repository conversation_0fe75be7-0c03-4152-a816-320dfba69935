import { NextRequest, NextResponse } from 'next/server';
import { z } from 'zod';

import { auth } from '@/lib/auth';
import prisma from '@/lib/db';
import { sendEmail } from '@/lib/email';
import {
  verificationCodeTemplate,
  type VerificationCodeData,
} from '@/lib/email-templates/verification-code';

// 生成6位随机验证码
function generateVerificationCode(): string {
  return Math.floor(100000 + Math.random() * 900000).toString();
}

// 发送验证码Schema
const sendCodeSchema = z.object({
  action: z.literal('send-withdrawal-verification'),
});

// 验证码验证Schema
const verifyCodeSchema = z.object({
  action: z.literal('verify-withdrawal-code'),
  verificationCode: z.string().length(6, '验证码必须为6位数字'),
});

export async function POST(request: NextRequest) {
  try {
    const session = await auth();
    if (!session?.user) {
      return NextResponse.json(
        { success: false, message: '未授权访问' },
        { status: 401 },
      );
    }

    const userId = (session.user as any).id;
    const body = await request.json();
    const { action } = body;

    // 发送提现验证码
    if (action === 'send-withdrawal-verification') {
      const validation = sendCodeSchema.safeParse(body);
      if (!validation.success) {
        return NextResponse.json(
          { success: false, message: '请求参数无效' },
          { status: 400 },
        );
      }

      // 获取用户信息
      const user = await prisma.user.findUnique({
        where: { id: userId },
        select: { email: true, name: true },
      });

      if (!user || !user.email) {
        return NextResponse.json(
          { success: false, message: '用户不存在或邮箱地址无效' },
          { status: 404 },
        );
      }

      // 生成验证码
      const verificationCode = generateVerificationCode();
      const expiresAt = new Date(Date.now() + 10 * 60 * 1000); // 10分钟后过期

      // 删除之前的验证码记录
      await prisma.verificationToken.deleteMany({
        where: {
          identifier: `${userId}:WITHDRAWAL_VERIFY`,
        },
      });

      // 创建新的验证码记录
      await prisma.verificationToken.create({
        data: {
          identifier: `${userId}:WITHDRAWAL_VERIFY`,
          token: verificationCode,
          expires: expiresAt,
        },
      });

      // 发送邮件
      try {
        const emailData: VerificationCodeData = {
          userName: user.name || '用户',
          userEmail: user.email,
          verificationCode,
          expiresIn: 10,
          action: 'verify-current-email' as const,
        };

        await sendEmail({
          to: user.email,
          subject: '提现验证码 - RefundGo',
          html: verificationCodeTemplate(emailData),
        });

        return NextResponse.json({
          success: true,
          message: '验证码已发送到您的邮箱，请查收',
        });
      } catch (emailError) {
        console.error('发送邮件失败:', emailError);
        return NextResponse.json(
          { success: false, message: '验证码发送失败，请重试' },
          { status: 500 },
        );
      }
    }

    // 验证提现验证码
    if (action === 'verify-withdrawal-code') {
      const validation = verifyCodeSchema.safeParse(body);
      if (!validation.success) {
        return NextResponse.json(
          {
            success: false,
            message: '数据验证失败',
            details: validation.error.issues.map(issue => issue.message),
          },
          { status: 400 },
        );
      }

      const { verificationCode } = validation.data;

      // 验证验证码
      const codeRecord = await prisma.verificationToken.findFirst({
        where: {
          identifier: `${userId}:WITHDRAWAL_VERIFY`,
          token: verificationCode,
          expires: {
            gt: new Date(),
          },
        },
      });

      if (!codeRecord) {
        return NextResponse.json(
          { success: false, message: '验证码无效或已过期' },
          { status: 400 },
        );
      }

      // 创建一个临时的提现授权token，有效期5分钟
      const withdrawalToken =
        generateVerificationCode() + Date.now().toString();
      const tokenExpiresAt = new Date(Date.now() + 5 * 60 * 1000); // 5分钟后过期

      // 删除验证码记录
      await prisma.verificationToken.delete({
        where: {
          identifier_token: {
            identifier: codeRecord.identifier,
            token: codeRecord.token,
          },
        },
      });

      // 创建提现授权token
      await prisma.verificationToken.create({
        data: {
          identifier: `${userId}:WITHDRAWAL_AUTH`,
          token: withdrawalToken,
          expires: tokenExpiresAt,
        },
      });

      return NextResponse.json({
        success: true,
        message: '邮箱验证成功',
        data: {
          withdrawalToken,
        },
      });
    }

    return NextResponse.json(
      { success: false, message: '无效的操作类型' },
      { status: 400 },
    );
  } catch (error) {
    console.error('提现验证处理失败:', error);
    return NextResponse.json(
      { success: false, message: '服务器内部错误' },
      { status: 500 },
    );
  }
}
