import { Resend } from 'resend';
import { NextRequest } from 'next/server';

import { getUserEmailLanguage } from '@/lib/email-language-detection';
import { getEmailTranslations } from '@/lib/email-translations';
import { verificationCodeTemplateI18n } from '@/lib/email-templates/verification-code-i18n';

import {
  notificationTemplate,
  verificationCodeTemplate,
  withdrawalApprovedTemplate,
  withdrawalRejectedTemplate,
  taskCompletedPublisherTemplate,
  taskCompletedAccepterTemplate,
  taskCancelledAccepterTemplate,
  taskReviewApprovedAccepterTemplate,
  taskReviewRejectedAccepterTemplate,
  taskCancelledPublisherTemplate,
  taskReviewPublisherTemplate,
  evidenceReviewPublisherTemplate,
  taskAcceptedPublisherTemplate,
  logisticsReviewPublisherTemplate,
  depositSuccessTemplateI18n,
  depositFailedTemplateI18n,
  withdrawalApprovedTemplateI18n,
  withdrawalRejectedTemplateI18n,
  taskCancelledPublisherTemplateI18n,
  taskCancelledAccepterTemplateI18n,
  taskAcceptedPublisherTemplateI18n,
  type NotificationEmailData,
  type VerificationCodeData,
  type WithdrawalApprovedEmailData,
  type WithdrawalRejectedEmailData,
  type TaskCompletedPublisherEmailData,
  type TaskCompletedAccepterEmailData,
  type TaskCancelledAccepterEmailData,
  type TaskReviewApprovedAccepterEmailData,
  type TaskReviewRejectedAccepterEmailData,
  type TaskCancelledPublisherEmailData,
  type TaskReviewPublisherEmailData,
  type EvidenceReviewPublisherEmailData,
  type TaskAcceptedPublisherEmailData,
  type LogisticsReviewPublisherEmailData,
  type DepositSuccessEmailData,
  type DepositFailedEmailData,
} from './email-templates';
import { verificationCodeI18nTemplate } from './email-templates/verification-code-i18n';

// 获取 Resend 客户端
function getResendClient() {
  if (!process.env.RESEND_API_KEY) {
    throw new Error('RESEND_API_KEY 环境变量未配置');
  }
  return new Resend(process.env.RESEND_API_KEY);
}

// 邮件类型定义
export interface EmailOptions {
  to: string | string[];
  subject: string;
  html?: string;
  text?: string;
  from?: string;
}

// 导出模板数据类型
export type {
  NotificationEmailData,
  VerificationCodeData,
  WithdrawalApprovedEmailData,
  WithdrawalRejectedEmailData,
  TaskCompletedPublisherEmailData,
  TaskCompletedAccepterEmailData,
  TaskCancelledAccepterEmailData,
  TaskReviewApprovedAccepterEmailData,
  TaskReviewRejectedAccepterEmailData,
  TaskCancelledPublisherEmailData,
  TaskReviewPublisherEmailData,
  EvidenceReviewPublisherEmailData,
  TaskAcceptedPublisherEmailData,
  LogisticsReviewPublisherEmailData,
  DepositSuccessEmailData,
  DepositFailedEmailData,
};

// 生成验证码
export function generateVerificationCode(): string {
  return Math.floor(100000 + Math.random() * 900000).toString();
}

// 发送邮件
export async function sendEmail(
  options: EmailOptions,
): Promise<{ success: boolean; messageId?: string; error?: string }> {
  try {
    if (!process.env.RESEND_API_KEY) {
      throw new Error('RESEND_API_KEY 环境变量未配置');
    }

    const defaultFrom = '<EMAIL>';
    const fromWithName = `RefundGo <${defaultFrom}>`;

    const emailData: any = {
      from: options.from || fromWithName,
      to: options.to,
      subject: options.subject,
    };

    if (options.html) {
      emailData.html = options.html;
    }

    if (options.text) {
      emailData.text = options.text;
    }

    const resend = getResendClient();
    const { data, error } = await resend.emails.send(emailData);

    if (error) {
      return { success: false, error: error.message };
    }

    return { success: true, messageId: data?.id };
  } catch (error) {
    return {
      success: false,
      error: error instanceof Error ? error.message : '未知错误',
    };
  }
}

// 欢迎邮件功能已移除

// 发送新用户通知邮件
export async function sendNewUserNotification(
  to: string,
  data: NotificationEmailData,
) {
  return await sendEmail({
    to,
    subject: '新用户注册通知 - RefundGo',
    html: notificationTemplate(data),
  });
}

// 发送验证码邮件 (with intelligent language detection)
export async function sendVerificationCodeEmail(
  to: string,
  data: VerificationCodeData,
  options?: {
    userId?: string;
    request?: NextRequest;
  },
) {
  // 🔍 添加详细调试日志
  console.log('\n🚀 开始验证码邮件发送流程 (sendVerificationCodeEmail)...');
  console.log(`📧 目标邮箱: ${to}`);
  console.log(`🎯 操作类型: ${data.action}`);
  console.log(`🌐 请求URL: ${options?.request?.url || '未提供'}`);
  console.log(`📍 Referer头部: ${options?.request?.headers.get('referer') || '未提供'}`);
  console.log(`🌍 Accept-Language头部: ${options?.request?.headers.get('accept-language') || '未提供'}`);
  console.log(`👤 用户ID: ${options?.userId || '未提供'}`);
  console.log(`📝 传入语言参数: ${data.language || '未设置'}`);

  // 使用智能语言检测 (如果没有提供语言参数)
  let language = data.language;
  if (!language && options?.request) {
    console.log('\n🔍 开始智能语言检测...');
    language = await getUserEmailLanguage(
      options.userId,
      options.request,
      'zh'
    );
    console.log(`✅ 检测到语言: ${language}`);
  } else if (!language) {
    language = 'zh'; // 默认中文
    console.log(`⚠️ 使用默认语言: ${language}`);
  } else {
    console.log(`📋 使用传入语言: ${language}`);
  }

  // 获取集中化翻译
  const translations = getEmailTranslations(language);
  console.log(`📝 使用翻译系统: ${language === 'zh' ? '中文' : '英文'}`);

  // 根据操作类型选择正确的邮件主题
  let emailSubject: string;
  switch (data.action) {
    case 'register':
      emailSubject = translations.notifications.verification.register.subject;
      break;
    case 'login':
      emailSubject = translations.notifications.verification.login.subject;
      break;
    case 'reset-password':
      emailSubject = translations.notifications.verification.resetPassword.subject;
      break;
    case 'verify-current-email':
      emailSubject = translations.notifications.verification.verifyEmail.subject;
      break;
    case 'change-email':
      emailSubject = translations.notifications.verification.changeEmail.subject;
      break;
    default:
      emailSubject = translations.notifications.verification.verifyEmail.subject;
  }

  // 更新邮件数据以包含检测到的语言
  const emailData = { ...data, language };

  console.log('\n📧 准备发送邮件...');
  console.log(`📬 邮件主题: ${emailSubject}`);
  console.log(`🎯 目标邮箱: ${to}`);
  console.log(`📋 邮件数据:`, {
    action: emailData.action,
    language: emailData.language,
    userName: emailData.userName,
    userEmail: emailData.userEmail,
    verificationCode: '******', // 隐藏验证码
    expiresIn: emailData.expiresIn,
  });

  return await sendEmail({
    to,
    subject: emailSubject,
    html: verificationCodeTemplateI18n(emailData),
  });
}

// 发送提现审核通过邮件
export async function sendWithdrawalApprovedEmail(
  to: string,
  data: WithdrawalApprovedEmailData,
) {
  const language = data.language || 'zh';

  const subjects = {
    zh: '提现审核通过通知 - RefundGo',
    en: 'Withdrawal Approved - RefundGo',
  };

  return await sendEmail({
    to,
    subject: subjects[language],
    html: withdrawalApprovedTemplateI18n(data),
  });
}

// 发送委托完成邮件给发布者
export async function sendTaskCompletedPublisherEmail(
  to: string,
  data: TaskCompletedPublisherEmailData,
) {
  return await sendEmail({
    to,
    subject: '委托完成通知 - RefundGo',
    html: taskCompletedPublisherTemplate(data),
  });
}

// 发送委托完成邮件给接单者
export async function sendTaskCompletedAccepterEmail(
  to: string,
  data: TaskCompletedAccepterEmailData,
) {
  return await sendEmail({
    to,
    subject: '委托完成通知 - RefundGo',
    html: taskCompletedAccepterTemplate(data),
  });
}

// 发送审核通过邮件给接单者
export async function sendTaskReviewApprovedAccepterEmail(
  to: string,
  data: TaskReviewApprovedAccepterEmailData,
) {
  return await sendEmail({
    to,
    subject: '审核通过通知 - RefundGo',
    html: taskReviewApprovedAccepterTemplate(data),
  });
}

// 发送审核驳回邮件给接单者
export async function sendTaskReviewRejectedAccepterEmail(
  to: string,
  data: TaskReviewRejectedAccepterEmailData,
) {
  return await sendEmail({
    to,
    subject: '审核驳回通知 - RefundGo',
    html: taskReviewRejectedAccepterTemplate(data),
  });
}

// 发送委托审核结果邮件给发布者
export async function sendTaskReviewPublisherEmail(
  to: string,
  data: TaskReviewPublisherEmailData,
) {
  return await sendEmail({
    to,
    subject: `委托审核${data.approved ? '通过' : '未通过'}通知 - RefundGo`,
    html: taskReviewPublisherTemplate(data),
  });
}

// 发送证据审核结果邮件给发布者
export async function sendEvidenceReviewPublisherEmail(
  to: string,
  data: EvidenceReviewPublisherEmailData,
) {
  return await sendEmail({
    to,
    subject: `证据审核${data.approved ? '通过' : '未通过'}通知 - RefundGo`,
    html: evidenceReviewPublisherTemplate(data),
  });
}

// 发送物流审核通知邮件给发布者
export async function sendLogisticsReviewPublisherEmail(
  to: string,
  data: LogisticsReviewPublisherEmailData,
) {
  return await sendEmail({
    to,
    subject: '订单物流信息待审核 - RefundGo',
    html: logisticsReviewPublisherTemplate(data),
  });
}

// 验证邮件地址格式
export function isValidEmail(email: string): boolean {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
}

// 批量发送邮件
export async function sendBulkEmails(
  emails: EmailOptions[],
): Promise<{ success: number; failed: number; results: any[] }> {
  const results = await Promise.allSettled(
    emails.map(email => sendEmail(email)),
  );

  const success = results.filter(
    result => result.status === 'fulfilled' && result.value.success,
  ).length;
  const failed = results.length - success;

  return {
    success,
    failed,
    results: results.map(result =>
      result.status === 'fulfilled'
        ? result.value
        : { success: false, error: result.reason },
    ),
  };
}

// 发送提现拒绝邮件
export async function sendWithdrawalRejectedEmail(
  to: string,
  data: WithdrawalRejectedEmailData,
) {
  const language = data.language || 'zh';

  const subjects = {
    zh: '提现申请被拒绝通知 - RefundGo',
    en: 'Withdrawal Request Rejected - RefundGo',
  };

  return await sendEmail({
    to,
    subject: subjects[language],
    html: withdrawalRejectedTemplateI18n(data),
  });
}

// 发送充值成功确认邮件 (with intelligent language detection)
export async function sendDepositSuccessEmail(
  to: string,
  data: DepositSuccessEmailData,
  options?: {
    userId?: string;
    request?: NextRequest;
  },
) {
  // Use intelligent language detection if no language is specified
  const language = data.language || await getUserEmailLanguage(
    options?.userId,
    options?.request,
    'zh'
  );

  const subjects = {
    zh: '充值成功确认 - RefundGo',
    en: 'Deposit Confirmation - RefundGo',
  };

  // Update data with detected language
  const emailData = { ...data, language };

  return await sendEmail({
    to,
    subject: subjects[language],
    html: depositSuccessTemplateI18n(emailData),
  });
}

// 发送充值失败通知邮件 (with intelligent language detection)
export async function sendDepositFailedEmail(
  to: string,
  data: DepositFailedEmailData,
  options?: {
    userId?: string;
    request?: NextRequest;
  },
) {
  // Use intelligent language detection if no language is specified
  const language = data.language || await getUserEmailLanguage(
    options?.userId,
    options?.request,
    'zh'
  );

  const subjects = {
    zh: '充值失败通知 - RefundGo',
    en: 'Deposit Failed - RefundGo',
  };

  // Update data with detected language
  const emailData = { ...data, language };

  return await sendEmail({
    to,
    subject: subjects[language],
    html: depositFailedTemplateI18n(emailData),
  });
}

// 发送委托取消通知邮件（发布者）
export async function sendTaskCancelledPublisherEmail(
  to: string,
  data: TaskCancelledPublisherEmailData,
) {
  const language = data.language || 'zh';

  const subjects = {
    zh: '委托取消通知 - RefundGo',
    en: 'Task Cancellation Notice - RefundGo',
  };

  return await sendEmail({
    to,
    subject: subjects[language],
    html: taskCancelledPublisherTemplateI18n(data),
  });
}

// 发送委托取消通知邮件（接单者）
export async function sendTaskCancelledAccepterEmail(
  to: string,
  data: TaskCancelledAccepterEmailData,
) {
  const language = data.language || 'zh';

  const subjects = {
    zh: '委托取消通知 - RefundGo',
    en: 'Task Cancellation Notice - RefundGo',
  };

  return await sendEmail({
    to,
    subject: subjects[language],
    html: taskCancelledAccepterTemplateI18n(data),
  });
}

// 发送委托被接受通知邮件
export async function sendTaskAcceptedPublisherEmail(
  to: string,
  data: TaskAcceptedPublisherEmailData,
) {
  const language = data.language || 'zh';

  const subjects = {
    zh: '委托被接受通知 - RefundGo',
    en: 'Task Accepted Notification - RefundGo',
  };

  return await sendEmail({
    to,
    subject: subjects[language],
    html: taskAcceptedPublisherTemplateI18n(data),
  });
}
