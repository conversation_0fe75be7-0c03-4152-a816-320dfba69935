# RefundGo 系统架构和技术栈

## 概述

RefundGo 是一个现代化的全栈 Web 应用程序，采用 Next.js
14+ 框架构建，集成了认证、支付、国际化等多个复杂系统。本文档详细介绍了系统的技术架构、技术选型和实现细节。

## 技术栈概览

### 前端技术栈

**核心框架**：

- **Next.js 14+** - React 全栈框架，支持 App Router
- **React 18** - 用户界面库，支持 Server Components
- **TypeScript** - 类型安全的 JavaScript 超集

**UI 和样式**：

- **Tailwind CSS** - 实用优先的 CSS 框架
- **shadcn/ui** - 高质量的 React 组件库
- **Radix UI** - 无样式的可访问组件基础
- **Lucide React** - 现代化图标库

**状态管理和数据获取**：

- **TanStack Query (React Query)** - 服务端状态管理
- **Zustand** - 轻量级客户端状态管理
- **React Hook Form** - 高性能表单库

**3D 和动画**：

- **React Three Fiber** - React 中的 Three.js
- **Framer Motion** - 动画和手势库
- **Three.js** - 3D 图形库

### 后端技术栈

**服务端框架**：

- **Next.js API Routes** - 服务端 API 端点
- **Node.js** - JavaScript 运行时环境

**数据库和 ORM**：

- **PostgreSQL** - 关系型数据库
- **Prisma** - 现代化数据库 ORM
- **Prisma Client** - 类型安全的数据库客户端

**认证和授权**：

- **NextAuth.js v5 (beta)** - 认证解决方案
- **bcryptjs** - 密码哈希加密
- **JWT** - JSON Web Tokens

### 第三方服务集成

**支付服务**：

- **YunPay** - 支付宝、微信支付
- **NOWPayments** - 加密货币支付
- **PayPal** - 国际支付解决方案

**邮件服务**：

- **Resend** - 现代化邮件发送服务

**文件存储**：

- **本地文件系统** - 文件上传和存储
- **可扩展云存储** - AWS S3、阿里云 OSS 等

### 开发工具和质量保证

**代码质量**：

- **ESLint** - JavaScript/TypeScript 代码检查
- **Prettier** - 代码格式化工具
- **TypeScript** - 静态类型检查

**测试框架**：

- **Jest** - JavaScript 测试框架
- **Testing Library** - React 组件测试
- **Playwright** - 端到端测试

**开发工具**：

- **Husky** - Git hooks 管理
- **lint-staged** - 暂存文件检查
- **Augment** - AI 辅助开发

## 系统架构设计

### 整体架构

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   前端应用       │    │   API 服务      │    │   数据库        │
│                │    │                │    │                │
│ - Next.js App  │◄──►│ - API Routes   │◄──►│ - PostgreSQL   │
│ - React UI     │    │ - 业务逻辑      │    │ - Prisma ORM   │
│ - 状态管理      │    │ - 认证授权      │    │ - 数据模型      │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         ▼                       ▼                       ▼
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   第三方服务     │    │   文件存储      │    │   邮件服务      │
│                │    │                │    │                │
│ - 支付网关      │    │ - 本地存储      │    │ - Resend       │
│ - OAuth 提供商  │    │ - 云存储        │    │ - 邮件模板      │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

### 应用层级结构

**路由架构**：

```
src/app/
├── (admin)/                 # 管理后台路由 (无 i18n)
│   └── admin/               # 管理后台页面
├── [locale]/                # 国际化路由 (en, zh)
│   ├── (main)/              # 公共页面 (首页, 登录等)
│   ├── (user)/              # 用户中心页面
│   └── payment/             # 支付流程页面
├── api/                     # API 路由
│   ├── admin/               # 管理后台 API
│   ├── auth/                # 认证 API
│   ├── cron/                # 定时任务 API
│   ├── user/                # 用户 API
│   └── ...                  # 其他 API
└── ...
```

### 数据库设计

**核心数据模型**：

**用户系统**：

- `User` - 用户基本信息
- `Account` - OAuth 账户关联
- `Session` - 用户会话管理
- `Wallet` - 用户钱包信息

**委托系统**：

- `Task` - 委托主体信息
- `TaskApplication` - 委托申请记录
- `TaskEvidence` - 委托证据管理
- `TaskReview` - 委托审核记录

**分类系统**：

- `Platform` - 电商平台信息
- `Category` - 商品分类
- `ChargebackType` - 拒付类型
- `PaymentMethod` - 支付方式

**财务系统**：

- `WalletTransaction` - 钱包交易记录
- `PaymentOrder` - 支付订单
- `Withdrawal` - 提现申请
- `SystemRate` - 系统费率配置

**支持系统**：

- `Ticket` - 工单信息
- `TicketReply` - 工单回复
- `ShopWhitelist` - 店铺白名单

### 安全架构

**认证流程**：

```
用户登录 → NextAuth 验证 → 生成 JWT → 存储会话 → 权限检查
```

**权限控制**：

- **路由级别**：中间件拦截未授权访问
- **API 级别**：每个端点验证用户权限
- **组件级别**：条件渲染基于用户角色
- **数据级别**：数据库查询包含权限过滤

**数据安全**：

- 密码使用 bcrypt 哈希存储
- 敏感数据加密传输（HTTPS）
- SQL 注入防护（Prisma ORM）
- XSS 防护（React 自动转义）
- CSRF 防护（NextAuth 内置）

## 核心业务逻辑

### 委托状态机

```typescript
enum TaskStatus {
  PENDING = 'PENDING', // 待审核
  REJECTED = 'REJECTED', // 审核拒绝
  RECRUITING = 'RECRUITING', // 招募中
  IN_PROGRESS = 'IN_PROGRESS', // 进行中
  PENDING_LOGISTICS = 'PENDING_LOGISTICS', // 等待物流
  PENDING_REVIEW = 'PENDING_REVIEW', // 等待审核
  PENDING_DELIVERY = 'PENDING_DELIVERY', // 等待收货
  COMPLETED = 'COMPLETED', // 已完成
  EXPIRED = 'EXPIRED', // 已过期
  CANCELLED = 'CANCELLED', // 已取消
}
```

**状态转换规则**：

- 只有管理员可以将 PENDING 转换为 RECRUITING 或 REJECTED
- 用户接单将 RECRUITING 转换为 IN_PROGRESS
- 委托执行过程中的状态转换有严格的时间限制
- 某些状态转换需要特定的条件满足

### 支付处理流程

**支付管理器模式**：

```typescript
interface PaymentProvider {
  createPayment(params: CreatePaymentParams): Promise<PaymentResult>;
  handleCallback(data: any): Promise<CallbackResult>;
  queryPaymentStatus(orderNo: string): Promise<PaymentStatus>;
}

class PaymentManager {
  private providers: Map<string, PaymentProvider>;

  async createPayment(params: CreatePaymentParams & { provider: string });
  async handleCallback(provider: string, data: any);
}
```

**支付流程**：

1. 用户发起支付请求
2. 系统创建支付订单
3. 调用对应支付提供商 API
4. 用户完成第三方支付
5. 接收支付回调通知
6. 验证支付结果
7. 更新订单状态
8. 处理业务逻辑

### 酬金计算系统

**费率结构**：

```typescript
interface CommissionCalculation {
  chargebackTypeRate: number; // 拒付类型费率
  paymentMethodRate: number; // 支付方式费率
  evidenceRate: number; // 证据费率
  totalRate: number; // 总费率
  commission: number; // 酬金金额
}
```

**计算逻辑**：

- 基础费率 + 分类费率 + 支付方式费率 + 证据费率
- 会员等级享受费率折扣
- 特殊委托可能有额外费率调整

## 性能优化策略

### 前端优化

**代码分割**：

- 路由级别的代码分割
- 组件懒加载
- 动态导入第三方库

**缓存策略**：

- 静态资源缓存
- API 响应缓存（React Query）
- 浏览器缓存优化

**渲染优化**：

- 服务端渲染（SSR）
- 静态生成（SSG）
- 增量静态再生（ISR）

### 后端优化

**数据库优化**：

- 索引优化
- 查询优化
- 连接池管理
- 读写分离（可扩展）

**API 优化**：

- 响应压缩
- 请求去重
- 批量操作
- 分页查询

**缓存策略**：

- Redis 缓存（可扩展）
- 应用级缓存
- CDN 缓存

## 监控和运维

### 错误监控

**错误捕获**：

- 全局错误边界
- API 错误处理
- 未捕获异常记录

**日志系统**：

- 结构化日志
- 日志级别分类
- 敏感信息脱敏

### 性能监控

**关键指标**：

- 页面加载时间
- API 响应时间
- 数据库查询性能
- 用户行为分析

**监控工具**：

- 应用性能监控（APM）
- 数据库性能监控
- 服务器资源监控

## 部署和扩展

### 部署架构

**生产环境**：

- 容器化部署（Docker）
- 负载均衡
- 数据库集群
- CDN 加速

**CI/CD 流程**：

- 代码提交触发构建
- 自动化测试
- 构建 Docker 镜像
- 部署到生产环境

### 扩展性设计

**水平扩展**：

- 无状态应用设计
- 数据库读写分离
- 微服务架构（未来）

**垂直扩展**：

- 服务器资源升级
- 数据库性能优化
- 缓存层扩展

---
