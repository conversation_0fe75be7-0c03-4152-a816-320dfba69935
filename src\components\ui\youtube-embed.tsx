"use client";

import { useState, useCallback } from "react";
import { Play, AlertCircle, RefreshCw } from "lucide-react";
import { Button } from "@/components/ui/button";

export interface YouTubeEmbedProps {
  videoId: string;
  title: string;
  className?: string;
  autoplay?: boolean;
  aspectRatio?: "16/9" | "4/3" | "1/1";
}

type LoadState = 'loading' | 'loaded' | 'error';

/**
 * Simple responsive YouTube iframe component
 * Uses the specific iframe code provided with responsive wrapper
 */
export function YouTubeEmbed({
  videoId,
  title,
  className = "",
  aspectRatio = "16/9"
}: YouTubeEmbedProps) {
  // Aspect ratio classes
  const aspectRatioClass = {
    "16/9": "aspect-video",
    "4/3": "aspect-[4/3]",
    "1/1": "aspect-square"
  }[aspectRatio];

  return (
    <div className={`relative ${aspectRatioClass} rounded-2xl overflow-hidden bg-gray-100 ${className}`}>
      {/* Direct iframe implementation with the provided code */}
      <iframe
        width="560"
        height="315"
        src={`https://www.youtube-nocookie.com/embed/${videoId}?si=K04XyDxdf2lCVyFS`}
        title="YouTube video player"
        frameBorder="0"
        allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share"
        referrerPolicy="strict-origin-when-cross-origin"
        allowFullScreen
        className="absolute inset-0 w-full h-full border-0"
      />
    </div>
  );
}

/**
 * YouTube Thumbnail Component
 * Shows a clickable thumbnail that loads the video when clicked
 */
export interface YouTubeThumbnailProps {
  videoId: string;
  title: string;
  className?: string;
  onClick?: () => void;
}

export function YouTubeThumbnail({ videoId, title, className = "", onClick }: YouTubeThumbnailProps) {
  const thumbnailUrl = `https://img.youtube.com/vi/${videoId}/maxresdefault.jpg`;
  
  return (
    <button
      type="button"
      onClick={onClick}
      className={`relative aspect-video rounded-2xl overflow-hidden bg-gray-100 group cursor-pointer ${className}`}
      aria-label={`Play video: ${title}`}
    >
      <img
        src={thumbnailUrl}
        alt={title}
        className="absolute inset-0 w-full h-full object-cover transition-transform group-hover:scale-105"
        loading="lazy"
      />
      <div className="absolute inset-0 bg-black/20 group-hover:bg-black/30 transition-colors flex items-center justify-center">
        <div className="w-16 h-16 rounded-full bg-red-600 group-hover:bg-red-700 transition-colors flex items-center justify-center shadow-lg">
          <Play className="w-6 h-6 text-white ml-1" fill="currentColor" />
        </div>
      </div>
    </button>
  );
}
