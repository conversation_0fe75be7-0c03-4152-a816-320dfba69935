"use client";

import { Menu, X, Globe, User, LogOut } from "lucide-react";
import { useTranslations } from "next-intl";
import { useState, useEffect, useMemo } from "react";

import { Button } from "@/components/ui/button";
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@/components/ui/dropdown-menu";
import { RefundGoLogoCompact } from "./refund-go-logo";

interface HeaderProps {
  isAuthenticated: boolean
  language: "zh" | "en"
  onLanguageChange: (lang: "zh" | "en") => void
  onAuthChange: (auth: boolean) => void
}

export function Header({ isAuthenticated, language, onLanguageChange, onAuthChange }: HeaderProps) {
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const [activeSection, setActiveSection] = useState("");
  const t = useTranslations("HomePage");

  const navigationItems = useMemo(() => [
    { id: "features", label: t("navigation.features"), href: "#features" },
    { id: "process", label: t("navigation.process"), href: "#process" },
    { id: "tutorial", label: t("navigation.tutorial"), href: "#tutorial" },
    { id: "tasks", label: t("navigation.taskHall"), href: "#tasks" },
    { id: "membership", label: t("navigation.membership"), href: "#membership" },
    { id: "faq", label: t("navigation.faq"), href: "#faq" },
  ], [t]);

  const scrollToSection = (href: string) => {
    const element = document.querySelector(href);
    if (element) {
      element.scrollIntoView({ behavior: "smooth" });
    }
    setIsMenuOpen(false);
  };

  useEffect(() => {
    const handleScroll = () => {
      const sections = navigationItems.map((item) => item.id);
      const currentSection = sections.find((section) => {
        const element = document.getElementById(section);
        if (element) {
          const rect = element.getBoundingClientRect();
          return rect.top <= 100 && rect.bottom >= 100;
        }
        return false;
      });
      if (currentSection) {
        setActiveSection(currentSection);
      }
    };

    window.addEventListener("scroll", handleScroll);
    return () => window.removeEventListener("scroll", handleScroll);
  }, [navigationItems]);

  return (
    <header className="fixed top-0 left-0 right-0 z-50 bg-white/95 backdrop-blur-sm border-b border-gray-200">
      <div className="container mx-auto px-4">
        <div className="flex items-center justify-between h-16">
          {/* Logo */}
          <div className="flex-shrink-0">
            <RefundGoLogoCompact animated={true} />
          </div>

          {/* Desktop Navigation */}
          <nav className="hidden md:flex items-center space-x-8">
            {navigationItems.map((item) => (
              <button
                key={item.id}
                onClick={() => scrollToSection(item.href)}
                className={`text-sm font-medium transition-colors hover:text-blue-600 ${
                  activeSection === item.id ? "text-blue-600" : "text-gray-700"
                }`}
              >
                {item.label}
              </button>
            ))}
          </nav>

          {/* Right Side Controls */}
          <div className="flex items-center space-x-4">
            {/* Language Switcher */}
            <Button
              variant="ghost"
              size="sm"
              onClick={() => onLanguageChange(language === "zh" ? "en" : "zh")}
              className="hidden sm:flex items-center space-x-1"
              aria-label={language === "zh" ? t("navigation.switchToEn") : t("navigation.switchToZh")}
              title={language === "zh" ? t("navigation.switchToEn") : t("navigation.switchToZh")}
            >
              <Globe className="h-4 w-4" />
              <span>{language === "zh" ? t("navigation.switchToEn") : t("navigation.switchToZh")}</span>
              <span className="sr-only">{t("navigation.currentLanguage")}: {t("navigation.currentLanguage")}</span>
            </Button>

            {/* Authentication Buttons */}
            {!isAuthenticated ? (
              <div className="hidden sm:flex items-center space-x-2">
                <Button variant="ghost" size="sm" onClick={() => onAuthChange(true)}>
                  {t("navigation.login")}
                </Button>
                <Button size="sm" onClick={() => onAuthChange(true)}>
                  {t("navigation.register")}
                </Button>
              </div>
            ) : (
              <div className="hidden sm:flex items-center space-x-2">
                <Button variant="ghost" size="sm">
                  {t("navigation.dashboard")}
                </Button>
                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <Button
                      variant="ghost"
                      size="sm"
                      aria-label={t("navigation.profile")}
                    >
                      <User className="h-4 w-4" />
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent align="end">
                    <DropdownMenuItem>{t("navigation.profile")}</DropdownMenuItem>
                    <DropdownMenuItem>{t("navigation.settings")}</DropdownMenuItem>
                    <DropdownMenuItem onClick={() => onAuthChange(false)}>
                      <LogOut className="h-4 w-4 mr-2" />
                      {t("navigation.logout")}
                    </DropdownMenuItem>
                  </DropdownMenuContent>
                </DropdownMenu>
              </div>
            )}

            {/* Mobile Menu Button */}
            <Button
              variant="ghost"
              size="sm"
              className="md:hidden"
              onClick={() => setIsMenuOpen(!isMenuOpen)}
              aria-label={isMenuOpen ? "Close menu" : "Open menu"}
              aria-expanded={isMenuOpen}
              aria-controls="mobile-menu"
            >
              {isMenuOpen ? <X className="h-5 w-5" /> : <Menu className="h-5 w-5" />}
            </Button>
          </div>
        </div>

        {/* Mobile Menu */}
        {isMenuOpen && (
          <div
            id="mobile-menu"
            className="md:hidden py-4 border-t border-gray-200 flex flex-col space-y-4"
            role="menu"
            aria-labelledby="mobile-menu-button"
          >
            {navigationItems.map((item) => (
              <button
                key={item.id}
                onClick={() => scrollToSection(item.href)}
                className="text-left text-gray-700 hover:text-blue-600 transition-colors focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 rounded-md px-2 py-1"
                role="menuitem"
              >
                {item.label}
              </button>
            ))}
            <div className="pt-4 border-t border-gray-200 space-y-2">
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => onLanguageChange(language === "zh" ? "en" : "zh")}
                  className="w-full justify-start"
                  aria-label={language === "zh" ? t("navigation.switchToEn") : t("navigation.switchToZh")}
                >
                  <Globe className="h-4 w-4 mr-2" />
                  {language === "zh" ? t("navigation.switchToEn") : t("navigation.switchToZh")}
                </Button>
                {!isAuthenticated ? (
                  <>
                    <Button
                      variant="ghost"
                      size="sm"
                      className="w-full justify-start"
                      onClick={() => onAuthChange(true)}
                    >
                      {t("navigation.login")}
                    </Button>
                    <Button size="sm" className="w-full" onClick={() => onAuthChange(true)}>
                      {t("navigation.register")}
                    </Button>
                  </>
                ) : (
                  <>
                    <Button variant="ghost" size="sm" className="w-full justify-start">
                      {t("navigation.dashboard")}
                    </Button>
                    <Button
                      variant="ghost"
                      size="sm"
                      className="w-full justify-start"
                      onClick={() => onAuthChange(false)}
                    >
                      <LogOut className="h-4 w-4 mr-2" />
                      {t("navigation.logout")}
                    </Button>
                  </>
                )}
              </div>
          </div>
        )}
      </div>
    </header>
  );
}
