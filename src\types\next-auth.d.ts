import NextAuth from 'next-auth';

// 扩展 NextAuth.js 的用户类型
declare module 'next-auth' {
  interface User {
    id: string;
    name?: string | null;
    email?: string | null;
    image?: string | null;
    role?: string | null;
    balance?: number | null;
    createdAt?: Date | null;
  }

  interface Session {
    user: {
      id: string;
      name?: string | null;
      email?: string | null;
      image?: string | null;
      role?: string | null;
      balance?: number | null;
      createdAt?: Date | null;
    } & DefaultSession['user'];
  }
}

declare module 'next-auth/jwt' {
  interface JWT {
    id: string;
    role?: string | null;
    balance?: number | null;
    createdAt?: Date | null;
  }
}

export {};
