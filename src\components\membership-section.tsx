"use client";

import { Check, Star } from "lucide-react";
import { useTranslations } from "next-intl";

import { Badge } from "@/components/ui/badge";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader } from "@/components/ui/card";


export function MembershipSection() {
  const t = useTranslations("HomePage");

  // Helper function to safely get features array from translations
  const getFeatures = (planKey: string): string[] => {
    try {
      const features = t.raw(`membership.plans.${planKey}.features`);
      return Array.isArray(features) ? features : [];
    } catch (error) {
      console.warn(`Failed to get features for plan ${planKey}:`, error);
      return [];
    }
  };

  const plans = [
    {
      id: "free",
      badge: null,
      title: t("membership.plans.free.name"),
      price: t("membership.plans.free.price"),
      period: t("membership.plans.free.period"),
      features: getFeatures("free"),
      buttonText: t("membership.choosePlan"),
      popular: false,
    },
    {
      id: "professional",
      badge: t("membership.mostPopular"),
      title: t("membership.plans.professional.name"),
      price: t("membership.plans.professional.price"),
      period: t("membership.plans.professional.period"),
      features: getFeatures("professional"),
      buttonText: t("membership.choosePlan"),
      popular: true,
    },
    {
      id: "business",
      badge: null,
      title: t("membership.plans.business.name"),
      price: t("membership.plans.business.price"),
      period: t("membership.plans.business.period"),
      features: getFeatures("business"),
      buttonText: t("membership.choosePlan"),
      popular: false,
    },
  ];

  return (
    <section id="pricing" className="py-20 bg-background dark:bg-background">
      <div className="container mx-auto px-4">
        {/* Section Header */}
        <div className="text-center mb-16">
          <h2 className="text-4xl md:text-5xl font-bold text-text-primary dark:text-text-primary mb-4">{t("membership.title")}</h2>
          <p className="text-xl text-text-secondary dark:text-text-secondary">{t("membership.subtitle")}</p>
        </div>

        {/* Pricing Cards */}
        <div className="grid md:grid-cols-3 gap-8 max-w-6xl mx-auto">
          {plans.map((plan) => (
            <Card
              key={plan.id}
              className={`relative hover:shadow-xl transition-all duration-300 ${
                plan.popular ? "ring-2 ring-blue-600 scale-105" : ""
              }`}
            >
              {plan.badge && (
                <div className="absolute -top-3 left-1/2 transform -translate-x-1/2">
                  <Badge className="bg-gradient-primary-from hover:bg-gradient-primary-from px-4 py-1 rounded-full">
                    <Star className="w-3 h-3 mr-1" />
                    {plan.badge}
                  </Badge>
                </div>
              )}

              <CardHeader className="text-center pb-4 bg-card dark:bg-card rounded-t-xl">
                <h3 className="text-2xl font-bold text-text-primary dark:text-text-primary mb-2">{plan.title}</h3>
                <div className="flex items-baseline justify-center">
                  <span className="text-4xl font-bold text-text-primary dark:text-text-primary">{plan.price}</span>
                  <span className="text-text-secondary dark:text-text-secondary ml-1">{plan.period}</span>
                </div>
              </CardHeader>

              <CardContent className="pt-4 bg-card dark:bg-card rounded-b-xl">
                {/* Features List */}
                <div className="bg-card/50 dark:bg-card/50 rounded-lg p-4 mb-6">
                  <ul className="space-y-4">
                    {(plan.features || []).map((feature, index) => (
                      <li key={index} className="flex items-start">
                        <Check className="w-5 h-5 text-success mr-3 mt-0.5 flex-shrink-0" />
                        <span className="text-text-secondary dark:text-text-secondary">{feature}</span>
                      </li>
                    ))}
                  </ul>
                </div>

                {/* Action Button */}
                <Button
                  className={`w-full rounded-lg ${
                    plan.popular ? "bg-blue-600 hover:bg-blue-700" : "bg-gray-900 hover:bg-gray-800"
                  }`}
                  size="lg"
                >
                  {plan.buttonText}
                </Button>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    </section>
  );
}
