# RefundGo 功能模块详细说明

## 概述

本文档详细介绍 RefundGo 平台的各个功能模块，包括技术实现、业务逻辑、用户交互和系统集成等方面的详细信息。

## 目录

1. [认证和授权系统](#认证和授权系统)
2. [委托管理系统](#委托管理系统)
3. [支付和钱包系统](#支付和钱包系统)
4. [会员管理系统](#会员管理系统)
5. [工单支持系统](#工单支持系统)
6. [白名单管理系统](#白名单管理系统)
7. [管理后台系统](#管理后台系统)
8. [国际化系统](#国际化系统)

---

## 认证和授权系统

### 1.1 技术架构

**核心技术**：

- NextAuth v5 (beta) - 认证框架
- Prisma - 数据库ORM
- JWT - 会话令牌
- bcryptjs - 密码加密

**认证提供商**：

- 邮箱/密码认证
- Google OAuth
- GitHub OAuth
- 可扩展其他OAuth提供商

### 1.2 权限控制系统

**角色定义**：

```typescript
enum UserRole {
  USER = 'USER',
  PREMIUM = 'PREMIUM',
  ADMIN = 'ADMIN',
}
```

**权限矩阵**：

- 基于角色的访问控制（RBAC）
- 细粒度权限定义
- 动态权限检查
- 中间件路由保护

**会话管理**：

- 数据库会话存储
- 自动会话刷新
- 设备管理和追踪
- 异常登录检测

### 1.3 安全特性

**密码安全**：

- 强密码策略
- bcrypt哈希加密
- 密码重置流程
- 登录失败锁定

**会话安全**：

- CSRF保护
- XSS防护
- 安全Cookie设置
- 会话超时管理

---

## 委托管理系统

### 2.1 委托生命周期

**状态流转图**：

```
PENDING → RECRUITING → IN_PROGRESS → PENDING_LOGISTICS
    → PENDING_REVIEW → PENDING_DELIVERY → COMPLETED
```

**状态说明**：

- `PENDING`: 管理员审核中
- `RECRUITING`: 公开招募接单者
- `IN_PROGRESS`: 委托执行中
- `PENDING_LOGISTICS`: 等待物流信息
- `PENDING_REVIEW`: 发布者审核中
- `PENDING_DELIVERY`: 等待确认收货
- `COMPLETED`: 委托完成

### 2.2 委托发布流程

**数据模型**：

```typescript
interface Task {
  id: string;
  title?: string;
  productUrl: string;
  productDescription: string;
  quantity: number;
  unitPrice: number;
  totalAmount: number;
  finalTotal: number;
  // ... 其他字段
}
```

**发布步骤**：

1. 基本信息填写
2. 分类和标签设置
3. 收货信息配置
4. 证据要求设定
5. 费用计算和确认
6. 提交审核

**费用计算逻辑**：

- 商品总价 = 单价 × 数量
- 平台酬金 = 基础费率 + 分类费率 + 支付方式费率
- 证据费 = 总价 × 证据费率（如适用）
- 最终费用 = 商品总价 + 平台酬金 + 证据费

### 2.3 委托接单机制

**接单条件**：

- 用户余额充足（支付押金）
- 委托状态为RECRUITING
- 用户未被限制接单

**押金机制**：

- 押金金额 = 委托总价的一定比例
- 押金冻结在用户账户
- 委托完成后自动释放
- 违约情况下可能扣除

### 2.4 证据管理系统

**证据类型**：

- 购物车截图
- 订单确认截图
- 支付凭证
- 物流跟踪信息
- 其他相关证据

**审核流程**：

- 自动初步检查
- 人工审核验证
- 审核结果通知
- 争议处理机制

---

## 支付和钱包系统

### 3.1 支付架构

**支付提供商**：

- YunPay - 支付宝、微信支付
- NOWPayments - 加密货币支付
- PayPal - 国际支付
- 可扩展其他提供商

**支付管理器**：

```typescript
class PaymentManager {
  private providers: Map<string, PaymentProvider>;

  async createPayment(params: CreatePaymentParams);
  async handleCallback(provider: string, data: any);
  async queryPaymentStatus(orderNo: string);
}
```

### 3.2 钱包系统

**账户结构**：

- 可用余额：可用于消费的金额
- 冻结金额：临时冻结的资金
- 总资产：可用余额 + 冻结金额

**交易类型**：

- DEPOSIT: 充值
- WITHDRAW: 提现
- TASK_FEE: 委托相关费用
- COMMISSION: 酬金收入
- MEMBERSHIP: 会员费用

**交易状态**：

- PENDING: 处理中
- COMPLETED: 已完成
- FAILED: 失败
- CANCELLED: 已取消

### 3.3 充值流程

**支持方式**：

- 支付宝：最低$10，手续费2%
- 微信支付：最低$10，手续费2%
- PayPal：最低$20，手续费3%
- 加密货币：最低$50，手续费1%

**处理流程**：

1. 用户选择充值金额和方式
2. 系统计算手续费和总支付金额
3. 创建支付订单
4. 跳转第三方支付页面
5. 支付完成后回调处理
6. 更新用户余额

### 3.4 提现流程

**提现方式**：

- 银行卡转账
- USDT (ERC20)
- USDT (TRC20)

**审核机制**：

- 自动风控检查
- 人工审核确认
- 批量处理执行
- 状态跟踪通知

---

## 会员管理系统

### 4.1 会员套餐

**套餐类型**：

**免费版 (FREE)**：

- 月委托限额：10个
- 标准酬金费率
- 基础客服支持
- 无白名单功能

**专业版 (PRO)**：

- 月委托限额：100个
- 酬金优惠：85折
- 白名单槽位：50个
- 优先客服支持
- 高级数据分析

**商业版 (BUSINESS)**：

- 月委托限额：500个
- 酬金优惠：75折
- 白名单槽位：200个
- 专属客服支持
- 完整数据分析
- API访问权限

### 4.2 会员权益

**功能权益**：

- 委托发布限额提升
- 酬金费率优惠
- 白名单管理功能
- 批量操作工具
- 数据分析报表

**服务权益**：

- 客服响应时间优先级
- 专属客服支持
- 优先提现处理
- 专业培训资源

### 4.3 订阅管理

**计费周期**：

- 月付：按月扣费
- 年付：一次性支付，享受折扣

**自动续费**：

- 到期前提醒
- 自动扣费续费
- 余额不足降级
- 手动取消订阅

---

## 工单支持系统

### 5.1 工单分类

**问题类型**：

- 技术支持：功能使用问题
- 账户问题：登录、密码、权限
- 支付问题：充值、提现、交易
- 委托纠纷：委托执行争议
- 功能建议：产品改进建议
- 其他问题：未分类问题

**优先级**：

- 低：一般咨询
- 中：功能问题
- 高：账户安全
- 紧急：系统故障

### 5.2 处理流程

**工单状态**：

- PENDING: 待处理
- IN_PROGRESS: 处理中
- WAITING_USER: 等待用户回复
- RESOLVED: 已解决
- CLOSED: 已关闭

**自动分配**：

- 根据问题类型分配
- 客服工作负载均衡
- 专业技能匹配
- 语言偏好匹配

### 5.3 服务等级协议

**响应时间**：

- 免费用户：24小时内响应
- 专业版用户：4小时内响应
- 商业版用户：1小时内响应
- 紧急问题：立即响应

**解决时间**：

- 简单问题：24小时内解决
- 复杂问题：72小时内解决
- 技术问题：需要开发介入

---

## 白名单管理系统

### 6.1 功能概述

**白名单用途**：

- 店铺信誉管理
- 风险控制
- 委托质量保证
- 合作伙伴管理

**管理权限**：

- 仅高级用户（PREMIUM）可用
- 根据会员等级分配槽位数量
- 支持批量导入和管理

### 6.2 数据结构

```typescript
interface ShopWhitelist {
  id: string;
  shopName: string;
  shopUrl: string;
  platform: string;
  status: WhitelistStatus;
  userId: string;
  reviewerId?: string;
  // ... 其他字段
}
```

**状态类型**：

- PENDING: 待审核
- APPROVED: 已通过
- REJECTED: 已拒绝
- SUSPENDED: 已暂停

### 6.3 审核机制

**审核标准**：

- 店铺真实性验证
- 经营状况评估
- 信誉度检查
- 合规性审查

**审核流程**：

1. 用户提交白名单申请
2. 系统初步验证
3. 管理员人工审核
4. 审核结果通知
5. 定期复审更新

---

## 管理后台系统

### 7.1 数据仪表盘

**关键指标**：

- 用户统计：注册数、活跃数、留存率
- 委托统计：发布数、完成数、成功率
- 财务统计：交易额、酬金收入、提现金额
- 系统统计：响应时间、错误率、可用性

**可视化图表**：

- 趋势线图：时间序列数据
- 饼图：分类占比
- 柱状图：对比数据
- 热力图：用户行为

### 7.2 用户管理

**用户信息**：

- 基本资料：姓名、邮箱、电话
- 账户状态：活跃、冻结、注销
- 会员信息：等级、到期时间
- 财务信息：余额、交易记录

**管理操作**：

- 账户状态变更
- 权限调整
- 余额调整
- 密码重置

### 7.3 委托审核

**审核队列**：

- 待审核委托列表
- 优先级排序
- 批量操作
- 审核历史

**审核工具**：

- 委托详情查看
- 风险评估工具
- 快速审核模板
- 审核意见记录

### 7.4 财务管理

**提现审核**：

- 提现申请列表
- 用户身份验证
- 风险评估检查
- 批量处理工具

**财务报表**：

- 收入支出统计
- 酬金收入分析
- 用户消费行为
- 盈利能力分析

---

## 国际化系统

### 8.1 多语言支持

**技术实现**：

- next-intl 框架
- 路由级别国际化
- 服务端渲染支持
- 客户端动态切换

**支持语言**：

- 中文简体 (zh)
- 英文 (en)
- 可扩展其他语言

### 8.2 本地化内容

**翻译范围**：

- 用户界面文本
- 错误消息
- 邮件模板
- 帮助文档
- 法律条款

**翻译管理**：

- 分布式翻译文件
- 命名空间组织
- 变量插值支持
- 复数形式处理

### 8.3 用户体验

**语言检测**：

- 浏览器语言自动检测
- 用户手动选择
- 登录状态记忆
- URL路径体现

**本地化适配**：

- 日期时间格式
- 数字货币格式
- 文本方向支持
- 文化习惯适配

---

## 系统集成和扩展

### 9.1 API设计

**RESTful API**：

- 统一的响应格式
- 错误码标准化
- 版本控制
- 文档自动生成

**认证授权**：

- JWT令牌验证
- API密钥管理
- 权限范围控制
- 频率限制

### 9.2 第三方集成

**支付集成**：

- 多支付提供商支持
- 统一支付接口
- 回调处理机制
- 异常重试机制

**邮件服务**：

- Resend邮件服务
- 模板化邮件
- 多语言支持
- 发送状态跟踪

### 9.3 监控和日志

**系统监控**：

- 性能指标监控
- 错误率统计
- 用户行为分析
- 资源使用监控

**日志管理**：

- 结构化日志
- 日志级别分类
- 敏感信息脱敏
- 日志归档和查询

---
