import { useQuery } from '@tanstack/react-query';

// 拒付类型费率接口
interface ChargebackType {
  id: string;
  name: string;
  rate: number;
  status: string;
}

// 支付方式费率接口
interface PaymentMethod {
  id: string;
  name: string;
  rate: number;
  status: string;
}

// 系统费率接口
interface SystemRate {
  id: string;
  noEvidenceExtraRate: number;
  depositRatio: number;
}

// 获取拒付类型费率
export function useChargebackTypes() {
  return useQuery({
    queryKey: ['chargeback-types'],
    queryFn: async (): Promise<ChargebackType[]> => {
      const response = await fetch('/api/user/chargeback-types');
      if (!response.ok) {
        throw new Error('获取拒付类型费率失败');
      }
      const data = await response.json();
      return data.data || [];
    },
    staleTime: 5 * 60 * 1000, // 5分钟缓存
  });
}

// 获取支付方式费率
export function usePaymentMethods() {
  return useQuery({
    queryKey: ['payment-methods'],
    queryFn: async (): Promise<PaymentMethod[]> => {
      const response = await fetch('/api/user/payment-methods');
      if (!response.ok) {
        throw new Error('获取支付方式费率失败');
      }
      const data = await response.json();
      return data.data || [];
    },
    staleTime: 5 * 60 * 1000, // 5分钟缓存
  });
}

// 获取系统费率
export function useSystemRate() {
  return useQuery({
    queryKey: ['system-rate'],
    queryFn: async (): Promise<SystemRate> => {
      const response = await fetch('/api/user/system-rates');
      if (!response.ok) {
        throw new Error('获取系统费率失败');
      }
      const data = await response.json();
      return data.data;
    },
    staleTime: 5 * 60 * 1000, // 5分钟缓存
  });
}

// 组合hook：获取所有酬金计算需要的费率数据
export function useCommissionRates() {
  const chargebackTypesQuery = useChargebackTypes();
  const paymentMethodsQuery = usePaymentMethods();
  const systemRateQuery = useSystemRate();

  return {
    chargebackTypes: chargebackTypesQuery.data || [],
    paymentMethods: paymentMethodsQuery.data || [],
    systemRate: systemRateQuery.data,
    isLoading:
      chargebackTypesQuery.isLoading ||
      paymentMethodsQuery.isLoading ||
      systemRateQuery.isLoading,
    error:
      chargebackTypesQuery.error ||
      paymentMethodsQuery.error ||
      systemRateQuery.error,
  };
}
