import { useQuery } from '@tanstack/react-query';

interface EvidenceFilters {
  search?: string;
  platform?: string;
  evidenceStatus?: string;
  page?: number;
  limit?: number;
}

interface EvidenceItem {
  taskId: string;
  platform: string;
  chargebackTypes: string[];
  submitDate: string;
  uploader: {
    nickname: string;
    email: string;
  };
  status: string;
  evidenceFiles: string[];
  evidenceUploadType: string;
  taskStatus: string;
}

interface EvidenceStats {
  totalEvidence: number;
  pendingReview: number;
  reviewed: number;
}

interface EvidenceResponse {
  success: boolean;
  data: {
    evidence: EvidenceItem[];
    stats: EvidenceStats;
    pagination: {
      page: number;
      limit: number;
      total: number;
      totalPages: number;
    };
  };
}

export function useEvidenceData(filters: EvidenceFilters = {}) {
  return useQuery<EvidenceResponse>({
    queryKey: ['evidence-data', filters],
    queryFn: async () => {
      const params = new URLSearchParams();

      if (filters.search) params.append('search', filters.search);
      if (filters.platform) params.append('platform', filters.platform);
      if (filters.evidenceStatus)
        params.append('evidenceStatus', filters.evidenceStatus);
      if (filters.page) params.append('page', filters.page.toString());
      if (filters.limit) params.append('limit', filters.limit.toString());

      const response = await fetch(`/api/admin/evidence?${params.toString()}`);

      if (!response.ok) {
        throw new Error('获取证据数据失败');
      }

      return response.json();
    },
    staleTime: 30 * 1000, // 30秒
    refetchOnWindowFocus: false,
  });
}

export type { EvidenceItem, EvidenceStats, EvidenceFilters };
