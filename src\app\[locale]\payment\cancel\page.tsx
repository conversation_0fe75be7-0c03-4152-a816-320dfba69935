'use client';

import { XCircle } from 'lucide-react';
import Link from 'next/link';

import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';

export default function PaymentCancelPage() {
  return (
    <div className='container mx-auto p-6 flex items-center justify-center min-h-[400px]'>
      <Card className='w-full max-w-md'>
        <CardHeader className='text-center'>
          <XCircle className='h-16 w-16 text-red-500 mx-auto mb-4' />
          <CardTitle className='text-2xl text-red-600'>支付已取消</CardTitle>
        </CardHeader>
        <CardContent className='text-center space-y-4'>
          <p className='text-muted-foreground'>
            您已取消支付，如有疑问请联系客服。
          </p>
          <div className='flex gap-4 justify-center'>
            <Button asChild>
              <Link href='/dashboard'>返回首页</Link>
            </Button>
            <Button variant='outline' asChild>
              <Link href='/payment'>重新支付</Link>
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
