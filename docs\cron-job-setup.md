# 定时任务配置指南

本项目使用定时任务来处理自动审核、自动确认收货等功能。

## 环境变量配置

### 1. 设置 CRON_SECRET

在生产环境中设置以下环境变量：

```bash
# 生成一个随机密钥
node -e "console.log(require('crypto').randomBytes(32).toString('base64'))"

# 将生成的密钥设置为环境变量
CRON_SECRET=your-generated-secret-key
```

## Vercel 部署配置

### 自动定时任务（推荐）

项目已配置 `vercel.json` 文件，部署到 Vercel 后会自动启用以下定时任务：

- **自动审核物流**: 每小时执行一次 (`0 * * * *`)
- **自动确认收货**: 每6小时执行一次 (`0 */6 * * *`)
- **过期任务清理**: 每天凌晨执行 (`0 0 * * *`)
- **会员过期处理**: 每天凌晨2点执行 (`0 2 * * *`)

## 外部定时任务服务配置

### 使用 cron-job.org

如果不使用 Vercel 或需要更灵活的调度，可以使用外部服务：

#### 1. 注册并获取 API 密钥

- 访问 [cron-job.org](https://cron-job.org)
- 注册账户并获取 API 密钥

#### 2. 创建定时任务

**自动审核物流任务**：

- URL: `https://your-domain.com/api/cron/auto-approve-reviews`
- 方法: POST
- 调度: `0 * * * *` (每小时)
- Headers: `Authorization: Bearer YOUR_CRON_SECRET`

**自动确认收货任务**：

- URL: `https://your-domain.com/api/cron/auto-confirm-delivery`
- 方法: POST
- 调度: `0 */6 * * *` (每6小时)
- Headers: `Authorization: Bearer YOUR_CRON_SECRET`

**过期任务清理**：

- URL: `https://your-domain.com/api/cron/expire-tasks`
- 方法: POST
- 调度: `0 0 * * *` (每天凌晨)
- Headers: `Authorization: Bearer YOUR_CRON_SECRET`

**会员过期处理**：

- URL: `https://your-domain.com/api/cron/expire-membership`
- 方法: POST
- 调度: `0 2 * * *` (每天凌晨2点)
- Headers: `Authorization: Bearer YOUR_CRON_SECRET`

#### 3. 使用 cron-job.org API 自动创建任务

```bash
# 设置变量
API_KEY="wqHdLbP8bd632DeO1o+PhYsbuyic3cUKaeSJYhOQb6E="
DOMAIN="https://your-domain.com"
CRON_SECRET="your-cron-secret"

# 创建自动审核任务
curl -X POST "https://api.cron-job.org/jobs" \
  -H "Authorization: Bearer $API_KEY" \
  -H "Content-Type: application/json" \
  -d '{
    "job": {
      "url": "'$DOMAIN'/api/cron/auto-approve-reviews",
      "enabled": true,
      "schedule": {
        "timezone": "Asia/Shanghai",
        "expiresAt": 0,
        "hours": [-1],
        "mdays": [-1],
        "minutes": [0],
        "months": [-1],
        "wdays": [-1]
      },
      "requestMethod": 2,
      "extendedData": {
        "headers": {
          "Authorization": "Bearer '$CRON_SECRET'"
        }
      },
      "title": "Auto Approve Reviews"
    }
  }'
```

## 手动触发（开发环境）

开发环境可以通过 GET 请求手动触发：

```bash
# 手动触发自动审核
curl http://localhost:3000/api/cron/auto-approve-reviews

# 手动触发自动确认收货
curl http://localhost:3000/api/cron/auto-confirm-delivery

# 手动触发过期任务清理
curl http://localhost:3000/api/cron/expire-tasks

# 手动触发会员过期处理
curl http://localhost:3000/api/cron/expire-membership
```

## 监控和日志

### 检查任务执行状态

所有定时任务都会在控制台输出执行日志，包括：

- 处理的记录数量
- 执行时间
- 错误信息（如有）

### 生产环境监控

建议设置以下监控：

1. 定时任务执行频率监控
2. API 响应时间监控
3. 错误率监控
4. 数据库性能监控

## 故障排除

### 常见问题

1. **401 Unauthorized**
   - 检查 `CRON_SECRET` 环境变量是否正确设置
   - 确认请求头中的 Authorization 格式正确

2. **任务未执行**
   - 检查定时任务配置是否正确
   - 确认服务器时区设置
   - 查看应用日志

3. **数据库连接错误**
   - 检查数据库连接配置
   - 确认数据库服务状态

### 调试步骤

1. 手动调用 API 端点测试
2. 检查应用日志
3. 验证数据库查询结果
4. 确认邮件发送功能

## 安全注意事项

1. **保护 CRON_SECRET**：不要在代码中硬编码，使用环境变量
2. **限制访问**：只允许可信的 IP 地址访问定时任务端点
3. **监控异常**：设置异常访问告警
4. **定期轮换**：定期更换 CRON_SECRET
