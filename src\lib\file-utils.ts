// 文件URL转换工具函数

// 将旧的静态文件URL转换为新的API文件服务URL
export function convertToApiFileUrl(fileUrl: string): string {
  if (!fileUrl) return fileUrl;

  // 如果已经是API文件服务URL，直接返回
  if (fileUrl.startsWith('/api/files/')) {
    return fileUrl;
  }

  // 如果是旧的静态文件URL，转换为API文件服务URL
  if (fileUrl.startsWith('/uploads/')) {
    return fileUrl.replace('/uploads/', '/api/files/');
  }

  // 如果是完整的URL，不做转换
  if (fileUrl.startsWith('http://') || fileUrl.startsWith('https://')) {
    return fileUrl;
  }

  // 其他情况，假设是相对路径的uploads文件
  if (fileUrl.startsWith('uploads/')) {
    return `/api/files/${fileUrl.substring(8)}`;
  }

  return fileUrl;
}

// 批量转换文件URL数组
export function convertFileUrlsToApiUrls(fileUrls: string[]): string[] {
  return fileUrls.map(convertToApiFileUrl);
}

// 检查文件URL是否有效
export function isValidFileUrl(fileUrl: string): boolean {
  if (!fileUrl) return false;

  // 检查是否是API文件服务URL
  if (fileUrl.startsWith('/api/files/')) {
    return true;
  }

  // 检查是否是静态文件URL
  if (fileUrl.startsWith('/uploads/')) {
    return true;
  }

  // 检查是否是完整的URL
  if (fileUrl.startsWith('http://') || fileUrl.startsWith('https://')) {
    return true;
  }

  return false;
}

// 从文件URL中提取文件名
export function extractFileNameFromUrl(fileUrl: string): string {
  if (!fileUrl) return '';

  const parts = fileUrl.split('/');
  return parts[parts.length - 1] || '';
}

// 从文件URL中提取文件扩展名
export function extractFileExtensionFromUrl(fileUrl: string): string {
  const fileName = extractFileNameFromUrl(fileUrl);
  const lastDotIndex = fileName.lastIndexOf('.');
  return lastDotIndex > -1 ? fileName.substring(lastDotIndex) : '';
}

// 判断文件是否为图片
export function isImageFile(fileUrl: string): boolean {
  const ext = extractFileExtensionFromUrl(fileUrl).toLowerCase();
  return ['.jpg', '.jpeg', '.png', '.gif', '.webp'].includes(ext);
}

// 判断文件是否为视频
export function isVideoFile(fileUrl: string): boolean {
  const ext = extractFileExtensionFromUrl(fileUrl).toLowerCase();
  return ['.mp4', '.avi', '.mov', '.wmv', '.flv', '.webm'].includes(ext);
}

// 获取文件类型
export function getFileType(fileUrl: string): 'image' | 'video' | 'other' {
  if (isImageFile(fileUrl)) return 'image';
  if (isVideoFile(fileUrl)) return 'video';
  return 'other';
}
