"use client";

import { ChevronDown, ChevronUp } from "lucide-react";
import { useTranslations } from "next-intl";
import { useState } from "react";

import { Card, CardContent } from "@/components/ui/card";

export function FAQSection() {
  const [openItems, setOpenItems] = useState<number[]>([]);
  const t = useTranslations("HomePage");

  const faqs = [
    {
      question: t("faq.questions.whatIsChargeback.question"),
      answer: t("faq.questions.whatIsChargeback.answer"),
    },
    {
      question: t("faq.questions.howToStart.question"),
      answer: t("faq.questions.howToStart.answer"),
    },
    {
      question: t("faq.questions.rewardSettlement.question"),
      answer: t("faq.questions.rewardSettlement.answer"),
    },
    {
      question: t("faq.questions.fundSecurity.question"),
      answer: t("faq.questions.fundSecurity.answer"),
    },
    {
      question: t("faq.questions.membershipDifferences.question"),
      answer: t("faq.questions.membershipDifferences.answer"),
    },
    {
      question: t("faq.questions.disputeTasks.question"),
      answer: t("faq.questions.disputeTasks.answer"),
    },
  ];

  const toggleItem = (index: number) => {
    setOpenItems((prev) => (prev.includes(index) ? prev.filter((i) => i !== index) : [...prev, index]));
  };

  return (
    <section id="faq" className="py-20 bg-muted dark:bg-muted">
      <div className="container mx-auto px-4">
        {/* Section Header */}
        <div className="text-center mb-16">
          <h2 className="text-4xl md:text-5xl font-bold text-text-primary dark:text-text-primary mb-4">{t("faq.title")}</h2>
          <p className="text-xl text-text-secondary dark:text-text-secondary">{t("faq.subtitle")}</p>
        </div>

        {/* FAQ Items */}
        <div className="max-w-4xl mx-auto space-y-4">
          {faqs.map((faq, index) => (
            <Card key={index} className="overflow-hidden bg-card dark:bg-card">
              <CardContent className="p-0">
                <button
                  type="button"
                  onClick={() => toggleItem(index)}
                  className="w-full px-6 py-4 text-left flex items-center justify-between hover:bg-muted dark:hover:bg-muted transition-colors duration-200"
                >
                  <h3 className="text-lg font-semibold text-text-primary dark:text-text-primary pr-4">{faq.question}</h3>
                  {openItems.includes(index) ? (
                    <ChevronUp className="w-5 h-5 text-text-muted dark:text-text-muted flex-shrink-0" />
                  ) : (
                    <ChevronDown className="w-5 h-5 text-text-muted dark:text-text-muted flex-shrink-0" />
                  )}
                </button>

                {openItems.includes(index) && (
                  <div className="px-6 pb-4">
                    <div className="border-t border-border dark:border-border pt-4">
                      <p className="text-text-secondary dark:text-text-secondary leading-relaxed">{faq.answer}</p>
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    </section>
  );
}
