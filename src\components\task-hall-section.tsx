"use client";

import { <PERSON>, <PERSON>, CreditCard, ArrowRight } from "lucide-react";
import Image from "next/image";
import { useTranslations } from "next-intl";
import { useState } from "react";

import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";


interface TaskHallSectionProps {
  isAuthenticated: boolean
}

export function TaskHallSection({ isAuthenticated }: TaskHallSectionProps) {
  const t = useTranslations("HomePage");
  const [imageErrors, setImageErrors] = useState<Record<number, boolean>>({});

  const tasks = [
    {
      id: 1,
      title: t("taskHall.products.wirelessHeadphones"),
      originalPrice: "$139.99",
      discountedPrice: "$108.99",
      rating: 4.5,
      reviewCount: 1,
      platform: t("taskHall.platforms.independentSite"),
      category: t("taskHall.tasks.electronics"),
      commission: "$27.20",
      chargebackType: t("taskHall.chargebackTypes.shippingViolation"),
      payment: t("taskHall.paymentMethods.creditCard"),
      image: "/products/headphones.svg",
      fallbackImage: "/placeholder.svg",
    },
    {
      id: 2,
      title: t("taskHall.products.electronicVapeKit"),
      originalPrice: "$169.99",
      discountedPrice: "$125.99",
      rating: 4.2,
      reviewCount: 10,
      platform: t("taskHall.platforms.independentSite"),
      category: t("taskHall.tasks.vape"),
      commission: "$31.50",
      chargebackType: t("taskHall.chargebackTypes.paypalViolation"),
      payment: t("taskHall.tasks.paypalPayment"),
      image: "/products/vape-kit.svg",
      fallbackImage: "/placeholder.svg",
    },
    {
      id: 3,
      title: t("taskHall.products.healthWellnessProduct"),
      originalPrice: "$299.99",
      discountedPrice: "$214.99",
      rating: 4.7,
      reviewCount: 1,
      platform: t("taskHall.platforms.independentSite"),
      category: t("taskHall.tasks.adult"),
      commission: "$53.80",
      chargebackType: t("taskHall.chargebackTypes.unhealthyProducts"),
      payment: t("taskHall.tasks.paypalPayment"),
      image: "/products/wellness.svg",
      fallbackImage: "/placeholder.svg",
    },
    {
      id: 4,
      title: t("taskHall.products.designerLeatherHandbag"),
      originalPrice: "$129.99",
      discountedPrice: "$94.99",
      rating: 4.3,
      reviewCount: 2,
      platform: t("taskHall.platforms.independentSite"),
      category: t("taskHall.tasks.bags"),
      commission: "$23.70",
      chargebackType: t("taskHall.chargebackTypes.infringingProducts"),
      payment: t("taskHall.paymentMethods.creditCard"),
      image: "/products/handbag.svg",
      fallbackImage: "/placeholder.svg",
    },
  ];

  const handleAcceptTask = (taskId: number) => {
    if (!isAuthenticated) {
      alert(t("taskHall.acceptNow"));
      return;
    }
    alert(`${t("taskHall.acceptNow")} ${taskId}`);
  };

  const handleImageError = (taskId: number) => {
    setImageErrors(prev => ({ ...prev, [taskId]: true }));
  };

  const getImageSrc = (task: typeof tasks[0]) => {
    return imageErrors[task.id] ? task.fallbackImage : task.image;
  };

  return (
    <section id="tasks" className="py-20 bg-background dark:bg-background">
      <div className="container mx-auto px-4">
        {/* Section Header */}
        <div className="text-center mb-16">
          <h2 className="text-4xl md:text-5xl font-bold text-text-primary dark:text-text-primary mb-4">{t("taskHall.title")}</h2>
          <p className="text-xl text-text-secondary dark:text-text-secondary">{t("taskHall.subtitle")}</p>
        </div>

        {/* Task Cards Grid */}
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 md:gap-6 mb-12">
          {tasks.map((task) => (
            <Card key={task.id} className="group hover:shadow-xl transition-all duration-300 hover:-translate-y-1 touch-manipulation bg-card dark:bg-card">
              <CardContent className="p-4">
                {/* Product Image */}
                <div className="relative mb-4">
                  <div className="relative w-full h-32 sm:h-40 rounded-lg overflow-hidden bg-muted dark:bg-muted">
                    <Image
                      src={getImageSrc(task)}
                      alt={`${task.title} - ${task.category} product image`}
                      fill
                      sizes="(max-width: 320px) 100vw, (max-width: 768px) 50vw, (max-width: 1024px) 33vw, 25vw"
                      className="object-cover transition-all duration-300 hover:scale-105"
                      loading="lazy"
                      quality={75}
                      onError={() => handleImageError(task.id)}
                      placeholder="blur"
                      blurDataURL="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAwIiBoZWlnaHQ9IjE1MCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cmVjdCB3aWR0aD0iMTAwJSIgaGVpZ2h0PSIxMDAlIiBmaWxsPSIjZjNmNGY2Ii8+PC9zdmc+"
                    />
                  </div>
                  <Badge className="absolute top-2 left-2 bg-success hover:bg-success text-xs z-10">
                    <Shield className="w-3 h-3 mr-1" />
                    {t("taskHall.verified")}
                  </Badge>
                </div>

                {/* Product Info */}
                <div className="space-y-3">
                  <h3 className="font-semibold text-text-primary dark:text-text-primary line-clamp-2">{task.title}</h3>

                  {/* Pricing */}
                  <div className="flex items-center space-x-2">
                    <span className="text-lg font-bold text-red-600 dark:text-red-400">{task.discountedPrice}</span>
                    <span className="text-sm text-text-muted dark:text-text-muted line-through">{task.originalPrice}</span>
                  </div>

                  {/* Rating */}
                  <div className="flex items-center space-x-1">
                    <div className="flex items-center">
                      <Star className="w-4 h-4 fill-yellow-400 text-yellow-400" />
                      <span className="text-sm font-medium ml-1">{task.rating}</span>
                    </div>
                    <span className="text-sm text-text-muted dark:text-text-muted">({task.reviewCount})</span>
                  </div>

                  {/* Platform & Category */}
                  <div className="flex items-center space-x-2">
                    <Badge variant="outline">{task.platform}</Badge>
                    <Badge variant="secondary">{task.category}</Badge>
                  </div>

                  {/* Commission */}
                  <div className="bg-success/10 dark:bg-success/20 p-2 rounded">
                    <div className="text-sm text-text-secondary dark:text-text-secondary">{t("taskHall.reward")}:</div>
                    <div className="text-lg font-bold text-success">{task.commission}</div>
                  </div>

                  {/* Chargeback Type */}
                  <div className="text-sm">
                    <span className="text-text-secondary dark:text-text-secondary">{t("taskHall.chargebackType")}: </span>
                    <span className="font-medium text-text-primary dark:text-text-primary">{task.chargebackType}</span>
                  </div>

                  {/* Payment Method */}
                  <div className="flex items-center text-sm">
                    <CreditCard className="w-4 h-4 mr-1" />
                    <span className="text-text-secondary dark:text-text-secondary">{t("taskHall.payment")}: </span>
                    <span className="font-medium ml-1 text-text-primary dark:text-text-primary">{task.payment}</span>
                  </div>

                  {/* Action Button */}
                  <Button className="w-full mt-4" onClick={() => handleAcceptTask(task.id)}>
                    {t("taskHall.acceptNow")}
                  </Button>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* View More Button */}
        <div className="text-center">
          <Button size="lg" variant="outline" className="px-8 bg-transparent border-border dark:border-border hover:bg-muted dark:hover:bg-muted">
            {t("taskHall.viewMore")}
            <ArrowRight className="ml-2 w-5 h-5" />
          </Button>
        </div>
      </div>
    </section>
  );
}
