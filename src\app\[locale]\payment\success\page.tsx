'use client';

import { <PERSON><PERSON>ir<PERSON>, Loader2 } from 'lucide-react';
import Link from 'next/link';
import { useSearchParams } from 'next/navigation';
import { useEffect, useState, Suspense } from 'react';

import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';

function PaymentSuccessContent() {
  const searchParams = useSearchParams();
  const [orderStatus, setOrderStatus] = useState<
    'loading' | 'success' | 'failed'
  >('loading');
  const orderId = searchParams.get('order_id');

  useEffect(() => {
    if (orderId) {
      // 模拟查询订单状态，实际项目中会调用API
      setTimeout(() => {
        setOrderStatus('success');
      }, 2000);
    } else {
      setOrderStatus('success'); // 如果没有订单ID，直接显示成功
    }
  }, [orderId]);

  if (orderStatus === 'loading') {
    return (
      <div className='container mx-auto p-6 flex items-center justify-center min-h-[400px]'>
        <div className='text-center'>
          <Loader2 className='h-8 w-8 animate-spin mx-auto mb-4' />
          <p>正在确认支付状态...</p>
        </div>
      </div>
    );
  }

  return (
    <div className='container mx-auto p-6 flex items-center justify-center min-h-[400px]'>
      <Card className='w-full max-w-md'>
        <CardHeader className='text-center'>
          <CheckCircle className='h-16 w-16 text-green-500 mx-auto mb-4' />
          <CardTitle className='text-2xl text-green-600'>
            {orderStatus === 'success' ? '支付成功' : '支付确认中'}
          </CardTitle>
        </CardHeader>
        <CardContent className='text-center space-y-4'>
          <p className='text-muted-foreground'>
            {orderStatus === 'success'
              ? '您的支付已成功处理，感谢您的购买！'
              : '支付正在处理中，请稍候或联系客服确认。'}
          </p>
          {orderId && (
            <div className='text-sm text-muted-foreground'>
              订单号：{orderId}
            </div>
          )}
          <div className='flex gap-4 justify-center'>
            <Button asChild>
              <Link href='/dashboard'>返回首页</Link>
            </Button>
            <Button variant='outline' asChild>
              <Link href='/orders'>查看订单</Link>
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}

export default function PaymentSuccessPage() {
  return (
    <Suspense
      fallback={
        <div className='container mx-auto p-6 flex items-center justify-center min-h-[400px]'>
          <div className='text-center'>
            <Loader2 className='h-8 w-8 animate-spin mx-auto mb-4' />
            <p>正在加载...</p>
          </div>
        </div>
      }
    >
      <PaymentSuccessContent />
    </Suspense>
  );
}
